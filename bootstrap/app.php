<?php

use App\Providers\AppServiceProvider;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

\Dotenv\Dotenv::createImmutable(($_ENV['APP_BASE_PATH'] ?? dirname(__DIR__)) . DIRECTORY_SEPARATOR, '.env.auth')->safeLoad();

# Load testing env if requested URL is `vorgaenge.testing`:
if (($_SERVER['HTTP_HOST'] ?? '') === 'vorgaenge.testing') {
    \Dotenv\Dotenv::createMutable(($_ENV['APP_BASE_PATH'] ?? dirname(__DIR__)) . DIRECTORY_SEPARATOR, '.env.testing')->load();
}

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        \Spatie\Permission\PermissionServiceProvider::class,
        \Lab404\Impersonate\ImpersonateServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
        then: function (): void {
            if (app()->environment('local')) {
                Route::middleware('web')
                    ->group(base_path('routes/dev.php'));
            }
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectUsersTo(AppServiceProvider::HOME);

        $middleware->throttleApi();
        $middleware->api([
            \App\Http\Middleware\MapExternalIdsInRequestAndResponse::class,
            \Illuminate\Cookie\Middleware\EncryptCookies::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \App\Http\Middleware\AuthenticateWithToken::class,
            'auth',
            \App\Http\Middleware\VerifyJsonMediaTypeHeaders::class,
            \App\Http\Middleware\SentryContext::class,
        ]);

        $middleware->group('public-api', [
            \App\Http\Middleware\MapExternalIdsInRequestAndResponse::class,
            \Illuminate\Cookie\Middleware\EncryptCookies::class,
            \Illuminate\Session\Middleware\StartSession::class,
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\VerifyJsonMediaTypeHeaders::class,
            \App\Http\Middleware\SentryContext::class,
        ]);

        $middleware->group('api-gateway', [
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\MapExternalIdsInRequestAndResponse::class,
            \App\Http\Middleware\AuthenticateWithToken::class,
            \App\Http\Middleware\VerifyJsonMediaTypeHeaders::class,
            \App\Http\Middleware\SentryContext::class,
        ]);

        $middleware->replace(\Illuminate\Foundation\Http\Middleware\TrimStrings::class, \App\Http\Middleware\TrimStrings::class);

        $middleware->alias([
            'auth' => \App\Http\Middleware\Authenticate::class,
            'debug' => \App\Http\Middleware\AllowOnlyIfDebugIsEnabled::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->withCommands([
        __DIR__.'/../app/Domain/Korrespondenz/Commands',
    ])
    ->create();
