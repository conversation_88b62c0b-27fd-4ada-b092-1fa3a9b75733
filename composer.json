{"name": "demv/professionalworks-sdk", "type": "library", "require": {"php": "^8.0", "demv/sdk-framework": "dev-main", "firebase/php-jwt": "^6.0", "guzzlehttp/guzzle": "^7.2", "illuminate/collections": "^8.0|^9.0|^10.0|^11.0", "nesbot/carbon": "^2.0"}, "suggests": {"illuminate/support": "When used in conjunction with a Laravel app."}, "require-dev": {"demvsystems/coding-standard": "dev-php8@dev", "filp/whoops": "^2.14", "illuminate/support": "^8|^9|^10", "middlewares/whoops": "^2.0", "mockery/mockery": "^1.4.0", "monolog/monolog": "^2.3", "php-di/php-di": "^6.3", "phpunit/phpunit": "^9.5", "slim/psr7": "^1.4", "slim/slim": "^4.8", "slim/twig-view": "^3.2", "spaceemotion/php-coding-standard": "dev-wip/v1", "symfony/var-dumper": "^5.3", "symplify/easy-coding-standard": "^9.4", "vlucas/phpdotenv": "^5.3"}, "autoload": {"psr-4": {"Demv\\ProfessionalworksSdk\\": "src/"}}, "autoload-dev": {"psr-4": {"Doku\\": "doku/src/", "Demv\\ProfessionalworksSdk\\": ["tests/unit", "tests/functional"]}}, "repositories": [{"type": "git", "url": "**************:demvsystems/coding-standard.git", "no-api": true}, {"type": "git", "url": "**************:demvsystems/sdk-framework.git", "no-api": true}], "extra": {"laravel": {"providers": ["Demv\\ProfessionalworksSdk\\ProfessionalWorksSDKProvider"]}}, "suggest": {"illuminate/support": "Allows using the Serviceprovider for easy Laravel Package Setup"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"ergebnis/composer-normalize": false, "dealerdirect/phpcodesniffer-composer-installer": false}}, "scripts": {"lint": "vendor/bin/phpcstd", "lint-fix": "vendor/bin/phpcstd --fix"}}