<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk;

use Demv\SdkFramework\Gateway\GatewayInterface;
use Generator;
use GuzzleHttp\Psr7\Response;
use PHPUnit\Framework\TestCase;

class PaginableRequestCollectionBuilderTest extends TestCase
{
    /**
     * @dataProvider paginatableRequestDataprovider
     *
     * @test
     */
    public function test_paginatable_request_lazy_collection(
        int $limit,
        int $amountPages,
        array $results,
        int $expectedNumberOfApiCalls,
        int $expectedResultCount,
    ): void {
        $gatewayMock = $this->getGatewayMock($results, $expectedNumberOfApiCalls);

        $result = PaginableRequestCollectionBuilder::build(
            'test',
            [
                'limit' => $limit,
                'offset' => 0,
            ],
            $gatewayMock,
            static fn (array $params) => $params,
            static fn (array $data) => $data,
            $amountPages,
        )->all();

        self::assertCount($expectedResultCount, $result);
    }

    public function paginatableRequestDataprovider(): Generator
    {
        $resultGenerator = static fn (int $count): array => array_fill(0, max(0, $count), [
            'foo' => 'bar',
        ]);

        yield 'If the overall count is 12 and the limit is 10 we need to call the api twice to get all data.' => [
            'limit' => 10,
            'amountPages' => 0,
            'results' => [$resultGenerator(10), $resultGenerator(2)],
            'expectedNumberOfApiCalls' => 2,
            'expectedResultCount' => 12,
        ];
        yield 'If the last page is "full" we still need to call the api again to check if there is more data.' => [
            'limit' => 2,
            'amountPages' => 0,
            'results' => [$resultGenerator(2), $resultGenerator(0)],
            'expectedNumberOfApiCalls' => 2,
            'expectedResultCount' => 2,
        ];
        yield 'If we limit the amount of pages, the api will only be called for the amount of pages.' => [
            'limit' => 5,
            'amountPages' => 2,
            'results' => [$resultGenerator(5), $resultGenerator(5), $resultGenerator(3)],
            'expectedNumberOfApiCalls' => 2,
            'expectedResultCount' => 10,
        ];
    }

    private function getGatewayMock(array $resultData, int $expectedNumberOfApiCalls): GatewayInterface
    {
        $gatewayMock = self::createMock(GatewayInterface::class);

        $responses = [];
        foreach ($resultData as $resultset) {
            $json = json_encode([
                'data' => [
                    'data' => $resultset,
                    'count' => count($resultset),
                ],
            ]);
            $responses[] = new Response(200, [], is_string($json) ? $json : '');
        }

        $gatewayMock
            ->expects(self::exactly($expectedNumberOfApiCalls))
            ->method('get')
            ->willReturnOnConsecutiveCalls(...$responses);

        return $gatewayMock;
    }
}
