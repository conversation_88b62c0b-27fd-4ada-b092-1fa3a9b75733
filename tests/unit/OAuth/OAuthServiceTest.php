<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\OAuth;

use Demv\ProfessionalworksSdk\Exceptions\OAuthException;
use Demv\ProfessionalworksSdk\Gateway\ProfessionalworksGateway;
use PHPUnit\Framework\TestCase;

/**
 * @coversDefaultClass \Demv\ProfessionalworksSdk\OAuth\OAuthService
 */
class OAuthServiceTest extends TestCase
{
    protected string $validToken = '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    protected string $validIncompleteToken = '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    protected string $invalidToken = '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    protected string $pubKey = '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnzyis1ZjfNB0bBgKFMSv
vkTtwlvBsaJq7S5wA+kzeVOVpVWwkWdVha4s38XM/pa/yr47av7+z3VTmvDRyAHc
aT92whREFpLv9cj5lTeJSibyr/Mrm/YtjCZVWgaOYIhwrXwKLqPr/11inWsAkfIy
tvHWTxZYEcXLgAXFuUuaS3uF9gEiNQwzGTU1v0FqkqTBr4B8nW3HCN47XUu0t8Y0
e+lf4s4OxQawWD79J9/5d3Ry0vbV3Am1FtGJiJvOwRsIfVChDpYStTcHTCMqtvWb
V6L11BWkpzGXSW4Hv43qa+GSYOD2QU68Mb59oSk2OB+BtOLpJofmbGEGgvmwyCI9
MwIDAQAB
-----END PUBLIC KEY-----';
    protected string $privKey = '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

    private OAuthService $oauthService;

    protected function setUp(): void
    {
        $gatewayMock = self::createMock(ProfessionalworksGateway::class);
        $this->oauthService = new OAuthService($this->pubKey, $gatewayMock, '');
    }

    /**
     * @covers ::validateToken
     * @test
     */
    public function valid_token(): void
    {
        $result = $this->oauthService->validateToken($this->validToken);
        $properties = get_object_vars($result);

        self::assertArrayHasKey('user_id', $properties);
        self::assertArrayHasKey('admin', $properties);
        self::assertArrayHasKey('exp', $properties);
        self::assertArrayHasKey('firstName', $properties);
        self::assertArrayHasKey('lastName', $properties);
    }

    /**
     * @covers ::validateToken
     * @test
     */
    public function valid_incomplete_token_token(): void
    {
        $this->expectException(OAuthException::class);
        $this->oauthService->validateToken($this->validIncompleteToken);
    }

    /**
     * @covers ::validateToken
     * @test
     */
    public function in_valid_token(): void
    {
        $this->expectException(OAuthException::class);
        $this->oauthService->validateToken($this->invalidToken);
    }
}
