<?php

declare(strict_types=1);

use PhpCsFixer\Fixer\ClassNotation\ClassAttributesSeparationFixer;
use PhpCsFixer\Fixer\ClassNotation\OrderedClassElementsFixer;
use PhpCsFixer\Fixer\Import\OrderedImportsFixer;
use PhpCsFixer\Fixer\Operator\BinaryOperatorSpacesFixer;
use PhpCsFixer\Fixer\Operator\NotOperatorWithSuccessorSpaceFixer;
use PhpCsFixer\Fixer\Phpdoc\PhpdocLineSpanFixer;
use PhpCsFixer\Fixer\PhpUnit\PhpUnitMethodCasingFixer;
use PhpCsFixer\Fixer\PhpUnit\PhpUnitTestAnnotationFixer;
use SlevomatCodingStandard\Sniffs\Arrays\DisallowImplicitArrayCreationSniff;
use SlevomatCodingStandard\Sniffs\Classes\ClassMemberSpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\DisallowMultiConstantDefinitionSniff;
use SlevomatCodingStandard\Sniffs\Classes\DisallowMultiPropertyDefinitionSniff;
use SlevomatCodingStandard\Sniffs\Classes\MethodSpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\ParentCallSpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\PropertySpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\RequireConstructorPropertyPromotionSniff;
use SlevomatCodingStandard\Sniffs\Classes\TraitUseSpacingSniff;
use SlevomatCodingStandard\Sniffs\ControlStructures\AssignmentInConditionSniff;
use SlevomatCodingStandard\Sniffs\ControlStructures\NewWithParenthesesSniff;
use SlevomatCodingStandard\Sniffs\ControlStructures\RequireNullSafeObjectOperatorSniff;
use SlevomatCodingStandard\Sniffs\Exceptions\RequireNonCapturingCatchSniff;
use SlevomatCodingStandard\Sniffs\Functions\RequireTrailingCommaInCallSniff;
use SlevomatCodingStandard\Sniffs\Functions\RequireTrailingCommaInDeclarationSniff;
use SlevomatCodingStandard\Sniffs\Functions\StaticClosureSniff;
use SlevomatCodingStandard\Sniffs\Functions\UnusedInheritedVariablePassedToClosureSniff;
use SlevomatCodingStandard\Sniffs\Namespaces\AlphabeticallySortedUsesSniff;
use SlevomatCodingStandard\Sniffs\Namespaces\NamespaceDeclarationSniff;
use SlevomatCodingStandard\Sniffs\Namespaces\NamespaceSpacingSniff;
use SlevomatCodingStandard\Sniffs\Operators\DisallowEqualOperatorsSniff;
use SlevomatCodingStandard\Sniffs\Operators\NegationOperatorSpacingSniff;
use SlevomatCodingStandard\Sniffs\PHP\UselessSemicolonSniff;
use SlevomatCodingStandard\Sniffs\TypeHints\UnionTypeHintFormatSniff;
use SlevomatCodingStandard\Sniffs\TypeHints\UselessConstantTypeHintSniff;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use Symplify\CodingStandard\Fixer\ArrayNotation\StandaloneLineInMultilineArrayFixer;
use Symplify\EasyCodingStandard\ValueObject\Option;
use Symplify\EasyCodingStandard\ValueObject\Set\SetList;

return static function (ContainerConfigurator $containerConfigurator): void {
    $parameters = $containerConfigurator->parameters();
    $parameters->set(Option::PATHS, [
        __DIR__ . '/app',
        __DIR__ . '/tests',
    ]);

    // FIXME: Caching is currently broken
    $parameters->set(Option::CLEAR_CACHE, true);

    $parameters->set(Option::SKIP, [
        StandaloneLineInMultilineArrayFixer::class,
        NotOperatorWithSuccessorSpaceFixer::class,
        OrderedClassElementsFixer::class,
        ClassAttributesSeparationFixer::class,
        BinaryOperatorSpacesFixer::class,
    ]);

    $containerConfigurator->import(SetList::SPACES);
    $containerConfigurator->import(SetList::ARRAY);
    $containerConfigurator->import(SetList::DOCBLOCK);
    $containerConfigurator->import(SetList::PSR_12);
    $containerConfigurator->import(SetList::STRICT);
    $containerConfigurator->import(SetList::CONTROL_STRUCTURES);
    $containerConfigurator->import(SetList::PHPUNIT);
    $containerConfigurator->import(SetList::NAMESPACES);

    $services = $containerConfigurator->services();

    $services->set(NegationOperatorSpacingSniff::class)
        ->property('spacesCount', 0);

    $services->set(OrderedImportsFixer::class)
        ->call('configure', [[
            'sort_algorithm' => 'alpha',
            'imports_order' => ['class', 'const', 'function'],
        ]]);

    $services->set(PhpdocLineSpanFixer::class)
        ->call('configure', [[
            'const' => 'single',
            'property' => 'single',
            'method' => 'multi',
        ]]);

    $services->set(PhpUnitMethodCasingFixer::class)
        ->call('configure', [[
            'case' => 'snake_case',
        ]]);

    $services->set(PhpUnitTestAnnotationFixer::class)
        ->call('configure', [[
            'style' => 'annotation',
        ]]);

    // $services->set(ParameterTypeHintSniff::class);
    // $services->set(PropertyTypeHintSniff::class);
    // $services->set(ReturnTypeHintSniff::class);

    $services->set(UselessConstantTypeHintSniff::class);
    $services->set(UnionTypeHintFormatSniff::class)
        ->property('withSpaces', 'no')
        ->property('shortNullable', true)
        ->property('nullPosition', 'last');

    $services->set(RequireNonCapturingCatchSniff::class);
    $services->set(DisallowImplicitArrayCreationSniff::class);

    $services->set(RequireConstructorPropertyPromotionSniff::class);
    $services->set(AssignmentInConditionSniff::class);

    $services->set(StaticClosureSniff::class);
    $services->set(DisallowEqualOperatorsSniff::class);

    $services->set(UnusedInheritedVariablePassedToClosureSniff::class);
    $services->set(UselessSemicolonSniff::class);

    $services->set(ClassMemberSpacingSniff::class)
        ->property('linesCountBetweenMembers', 1);

    $services->set(DisallowMultiConstantDefinitionSniff::class);
    $services->set(DisallowMultiPropertyDefinitionSniff::class);

    $services->set(MethodSpacingSniff::class)
        ->property('minLinesCount', 1)
        ->property('maxLinesCount', 1);

    $services->set(ParentCallSpacingSniff::class)
        ->property('linesCountBefore', 1)
        ->property('linesCountBeforeFirst', 0)
        ->property('linesCountAfter', 1)
        ->property('linesCountAfterLast', 0);

    $services->set(PropertySpacingSniff::class)
        ->property('minLinesCountBeforeWithComment', 1)
        ->property('maxLinesCountBeforeWithComment', 1)
        ->property('minLinesCountBeforeWithoutComment', 0)
        ->property('maxLinesCountBeforeWithoutComment', 1);

    //    $services->set(ConstantSpacingSniff::class)
    //        ->property('minLinesCountBeforeWithComment', 1)
    //        ->property('maxLinesCountBeforeWithComment', 1)
    //        ->property('minLinesCountBeforeWithoutComment', 0)
    //        ->property('maxLinesCountBeforeWithoutComment', 1);

    $services->set(TraitUseSpacingSniff::class)
        ->property('linesCountBeforeFirstUse', 0)
        ->property('linesCountBeforeFirstUseWhenFirstInClass', 0)
        ->property('linesCountBetweenUses', 0)
        ->property('linesCountAfterLastUse', 1)
        ->property('linesCountAfterLastUseWhenLastInClass', 0);

//    $services->set(BlockControlStructureSpacingSniff::class)
//        ->property('linesCountBefore', 1)
//        ->property('linesCountBeforeFirst', 0)
//        ->property('linesCountAfter', 1)
//        ->property('linesCountAfterLast', 0);

    $services->set(NewWithParenthesesSniff::class);
    $services->set(RequireNullSafeObjectOperatorSniff::class);
    $services->set(RequireTrailingCommaInCallSniff::class);
    $services->set(RequireTrailingCommaInDeclarationSniff::class);
    $services->set(AlphabeticallySortedUsesSniff::class);
    $services->set(NamespaceDeclarationSniff::class);
    $services->set(NamespaceSpacingSniff::class);
};
