<?php

declare(strict_types=1);

use PHP_CodeSniffer\Standards\Squiz\Sniffs\WhiteSpace\OperatorSpacingSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\AssertsSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\BackticksSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\CallbackFunctionsSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\CryptoFunctionsSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\EasyRFISniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\EasyXSSSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\ErrorHandlingSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\FilesystemFunctionsSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\FringeFunctionsSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\FunctionHandlingFunctionsSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\MysqliSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\NoEvalsSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\PhpinfosSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\PregReplaceSniff;
use PHPCS_SecurityAudit\Sniffs\BadFunctions\SQLFunctionsSniff;
use PHPCS_SecurityAudit\Sniffs\Misc\BadCorsHeaderSniff;
use PHPCS_SecurityAudit\Sniffs\Misc\IncludeMismatchSniff;
use PhpCsFixer\Fixer\ClassNotation\ClassAttributesSeparationFixer;
use PhpCsFixer\Fixer\ClassNotation\OrderedClassElementsFixer;
use PhpCsFixer\Fixer\FunctionNotation\FunctionTypehintSpaceFixer;
use PhpCsFixer\Fixer\Import\OrderedImportsFixer;
use PhpCsFixer\Fixer\Operator\BinaryOperatorSpacesFixer;
use PhpCsFixer\Fixer\Operator\NotOperatorWithSuccessorSpaceFixer;
use PhpCsFixer\Fixer\Phpdoc\PhpdocLineSpanFixer;
use PhpCsFixer\Fixer\PhpUnit\PhpUnitMethodCasingFixer;
use PhpCsFixer\Fixer\PhpUnit\PhpUnitTestAnnotationFixer;
use SlevomatCodingStandard\Sniffs\Arrays\DisallowImplicitArrayCreationSniff;
use SlevomatCodingStandard\Sniffs\Classes\ClassMemberSpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\ConstantSpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\DisallowMultiConstantDefinitionSniff;
use SlevomatCodingStandard\Sniffs\Classes\DisallowMultiPropertyDefinitionSniff;
use SlevomatCodingStandard\Sniffs\Classes\MethodSpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\ParentCallSpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\PropertySpacingSniff;
use SlevomatCodingStandard\Sniffs\Classes\RequireConstructorPropertyPromotionSniff;
use SlevomatCodingStandard\Sniffs\Classes\TraitUseSpacingSniff;
use SlevomatCodingStandard\Sniffs\ControlStructures\AssignmentInConditionSniff;
use SlevomatCodingStandard\Sniffs\ControlStructures\BlockControlStructureSpacingSniff;
use SlevomatCodingStandard\Sniffs\ControlStructures\NewWithParenthesesSniff;
use SlevomatCodingStandard\Sniffs\ControlStructures\RequireNullSafeObjectOperatorSniff;
use SlevomatCodingStandard\Sniffs\Exceptions\RequireNonCapturingCatchSniff;
use SlevomatCodingStandard\Sniffs\Functions\RequireTrailingCommaInCallSniff;
use SlevomatCodingStandard\Sniffs\Functions\RequireTrailingCommaInDeclarationSniff;
use SlevomatCodingStandard\Sniffs\Functions\StaticClosureSniff;
use SlevomatCodingStandard\Sniffs\Functions\UnusedInheritedVariablePassedToClosureSniff;
use SlevomatCodingStandard\Sniffs\Namespaces\AlphabeticallySortedUsesSniff;
use SlevomatCodingStandard\Sniffs\Namespaces\NamespaceDeclarationSniff;
use SlevomatCodingStandard\Sniffs\Namespaces\NamespaceSpacingSniff;
use SlevomatCodingStandard\Sniffs\Operators\DisallowEqualOperatorsSniff;
use SlevomatCodingStandard\Sniffs\Operators\NegationOperatorSpacingSniff;
use SlevomatCodingStandard\Sniffs\PHP\ReferenceSpacingSniff;
use SlevomatCodingStandard\Sniffs\PHP\UselessSemicolonSniff;
use SlevomatCodingStandard\Sniffs\TypeHints\ParameterTypeHintSniff;
use SlevomatCodingStandard\Sniffs\TypeHints\PropertyTypeHintSniff;
use SlevomatCodingStandard\Sniffs\TypeHints\ReturnTypeHintSniff;
use SlevomatCodingStandard\Sniffs\TypeHints\UnionTypeHintFormatSniff;
use SlevomatCodingStandard\Sniffs\TypeHints\UselessConstantTypeHintSniff;
use Symplify\CodingStandard\Fixer\ArrayNotation\StandaloneLineInMultilineArrayFixer;
use Symplify\EasyCodingStandard\Config\ECSConfig;
use Symplify\EasyCodingStandard\ValueObject\Option;
use Symplify\EasyCodingStandard\ValueObject\Set\SetList;

// Commented broken sniffers, try to fix with php 8.2 update


return static function (ECSConfig $ecsConfig): void {

    $ecsConfig->paths([
        __DIR__ . '/app',
        __DIR__ . '/database',
        __DIR__ . '/routes',
        __DIR__ . '/tests',
    ]);

    $ecsConfig->skip([
        StandaloneLineInMultilineArrayFixer::class,
        NotOperatorWithSuccessorSpaceFixer::class,
        OrderedClassElementsFixer::class,
        ClassAttributesSeparationFixer::class,
        BinaryOperatorSpacesFixer::class,
        FunctionTypehintSpaceFixer::class,
    ]);

    $ecsConfig->sets([
        SetList::SPACES,
        SetList::ARRAY,
        SetList::DOCBLOCK,
        SetList::STRICT,
        SetList::CONTROL_STRUCTURES,
        SetList::PHPUNIT,
        SetList::NAMESPACES,
    ]);

    // rule sniffs
    $ecsConfig->rules([
            UselessConstantTypeHintSniff::class,
            ParameterTypeHintSniff::class,
            RequireNonCapturingCatchSniff::class,
            DisallowImplicitArrayCreationSniff::class,
            RequireConstructorPropertyPromotionSniff::class,
            AssignmentInConditionSniff::class,
            StaticClosureSniff::class,
            DisallowEqualOperatorsSniff::class,
            UnusedInheritedVariablePassedToClosureSniff::class,
            UselessSemicolonSniff::class,
            DisallowMultiConstantDefinitionSniff::class,
            DisallowMultiPropertyDefinitionSniff::class,
            ReferenceSpacingSniff::class,
            NewWithParenthesesSniff::class,
            RequireNullSafeObjectOperatorSniff::class,
            RequireTrailingCommaInCallSniff::class,
            RequireTrailingCommaInDeclarationSniff::class,
            AlphabeticallySortedUsesSniff::class,
            NamespaceDeclarationSniff::class,
            NamespaceSpacingSniff::class,
        ]
    );

    // security sniffs
    $ecsConfig->rules([
            AssertsSniff::class,
            BackticksSniff::class,
            CallbackFunctionsSniff::class,
            CryptoFunctionsSniff::class,
            EasyRFISniff::class,
            EasyXSSSniff::class,
            ErrorHandlingSniff::class,
            FilesystemFunctionsSniff::class,
            FringeFunctionsSniff::class,
            FunctionHandlingFunctionsSniff::class,
            MysqliSniff::class,
            NoEvalsSniff::class,
            PhpinfosSniff::class,
            PregReplaceSniff::class,
            SQLFunctionsSniff::class,
            BadCorsHeaderSniff::class,
            IncludeMismatchSniff::class,
        ]
    );

    // rules with config
    $ecsConfig->ruleWithConfiguration(NegationOperatorSpacingSniff::class, [
        'spacesCount' => 0
    ]);
    $ecsConfig->ruleWithConfiguration(OrderedImportsFixer::class, [
        'sort_algorithm' => 'alpha',
        'imports_order' => ['class', 'const', 'function'],
    ]);
    $ecsConfig->ruleWithConfiguration(PhpdocLineSpanFixer::class, [
        'const' => 'single',
        'property' => 'single',
        'method' => 'multi',
    ]);
    $ecsConfig->ruleWithConfiguration(PhpUnitMethodCasingFixer::class, [
        'case' => 'snake_case',
    ]);
    $ecsConfig->ruleWithConfiguration(PhpUnitTestAnnotationFixer::class, [
        'style' => 'annotation',
    ]);
    $ecsConfig->ruleWithConfiguration(UnionTypeHintFormatSniff::class, [
        'withSpaces' => 'no',
        'shortNullable' => true,
        'nullPosition' => 'last',
    ]);
    $ecsConfig->ruleWithConfiguration(ClassMemberSpacingSniff::class, [
        'linesCountBetweenMembers' => 1
    ]);
    $ecsConfig->ruleWithConfiguration(MethodSpacingSniff::class, [
        'maxLinesCount' => 1,
        'minLinesCount' => 1,
    ]);
    $ecsConfig->ruleWithConfiguration(ParentCallSpacingSniff::class, [
        'linesCountBefore' => 1,
        'linesCountBeforeFirst' => 0,
        'linesCountAfter' => 1,
        'linesCountAfterLast' => 0,
    ]);
    $ecsConfig->ruleWithConfiguration(PropertySpacingSniff::class, [
        'minLinesCountBeforeWithComment' => 1,
        'maxLinesCountBeforeWithComment' => 1,
        'minLinesCountBeforeWithoutComment' => 0,
        'maxLinesCountBeforeWithoutComment' => 1,
    ]);

    $ecsConfig->ruleWithConfiguration(ConstantSpacingSniff::class, [
        'minLinesCountBeforeWithComment' => 1,
        'maxLinesCountBeforeWithComment' => 1,
        'minLinesCountBeforeWithoutComment' => 0,
        'maxLinesCountBeforeWithoutComment' => 1,
    ]);
    $ecsConfig->ruleWithConfiguration(TraitUseSpacingSniff::class, [
        'linesCountBeforeFirstUse' => 0,
        'linesCountBeforeFirstUseWhenFirstInClass' => 0,
        'linesCountBetweenUses' => 0,
        'linesCountAfterLastUse' => 1,
        'linesCountAfterLastUseWhenLastInClass' => 0,
    ]);
    $ecsConfig->ruleWithConfiguration(BlockControlStructureSpacingSniff::class, [
        'linesCountBefore' => 1,
        'linesCountBeforeFirst' => 0,
        'linesCountAfter' => 1,
        'linesCountAfterLast' => 0,
    ]);

// Fügt aktuell spaces zu declare(strict_types=1); hinzu in einigen files
//    $ecsConfig->ruleWithConfiguration(OperatorSpacingSniff::class, [
//        'ignoreNewlines' => 1,
//    ]);

    $ecsConfig->ruleWithConfiguration(ReturnTypeHintSniff::class, [
        'enableIntersectionTypeHint' => 0,
    ]);

    $ecsConfig->ruleWithConfiguration(PropertyTypeHintSniff::class, [
        'enableIntersectionTypeHint' => 0,
    ]);
};
