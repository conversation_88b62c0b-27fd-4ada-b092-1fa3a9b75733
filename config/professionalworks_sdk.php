<?php

return [
    'baseUrl' => env('PW_BASE_URL', 'http://professionalworks.local'),
    'token' => env('PW_API_TOKEN'),
    'origin' => env('APP_NAME'),
    'oauth' => [
        'loginUrl' => env('PW_OAUTH_LOGIN_URL', 'auth/shortlived/token'),
        'publicKey' => env('PW_PUBLIC_KEY'),
    ],
    'proxy' => [
        'user' => env('PW_PROXY_USER', ''),
        'password' => env('PW_PROXY_PASSWORD', ''),
        'baseUrl' => env('PW_PROXY_URL', ''),
    ],
    // Define own Hosts, like in the /etc/hosts file, to skip the DNS-Resolving
    //Schema: '['professionalworks.local:80:127.0.0.1']'
    'hosts' => [],
];
