<template>
  <div>
    <SidebarCta
      :action-handler="actionHandler"
      :action-icon="actionIcon"
      :disabled="disabled"
      :header="header"
      :label="label"
    >
      <template #action="{reference}">
        <slot
          name="action"
          :reference="reference"
        />
      </template>
      <template
        v-if="title"
        #label-addon
      >
        <ElPopover
          placement="top"
          :width="300"
          trigger="hover"
          :content="title"
        >
          <template #reference>
            <DsIcon
              name="info-circle"
              class="ml-1 cursor-help text-gray-500"
            />
          </template>
        </ElPopover>
      </template>
    </SidebarCta>

    <slot/>
  </div>
</template>

<script setup lang="ts">
import { DsIcon } from '@demvsystems/design-components';
import { ElPopover } from 'element-plus';
import { useTemplateRef } from 'vue';

import SidebarCta from '@/pages/vorgang/components/SidebarCta.vue';

defineProps<{
  label: string,
  actionIcon?: string,
  actionHandler?: (payload: MouseEvent) => void,
  disabled?: boolean,
  title?: string,
}>();

const header = useTemplateRef<HTMLElement>('header');
</script>
