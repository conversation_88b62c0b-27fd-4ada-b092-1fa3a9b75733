<template>
  <DsForm
    class="space-y-5"
    :validation-errors="validationErrors"
  >
    <div class="space-y-7">
      <div class="space-y-3">
        <DsSwitch v-model="hasDetails">
          Termindetails hinterlegen
        </DsSwitch>
        <FormSection
          v-if="hasDetails === true"
          title="Termindetails"
        >
          <span class="flex flex-col space-y-3">
            <DsFormGroup
              label="Art"
              class="shrink-0"
              data-test="aktionen__gespraechstyp"
            >
              <DsRadioGroup
                v-model="type"
                variant="button"
              >
                <DsRadioButton
                  :value="GespraechsType.OnSite"
                  icon="home"
                >
                  Vor Ort
                </DsRadioButton>

                <DsRadioButton
                  :value="GespraechsType.Phone"
                  icon="phone"
                >
                  Telefon
                </DsRadioButton>

                <DsRadioButton
                  :value="GespraechsType.Other"
                >
                  Sonstige
                </DsRadioButton>
              </DsRadioGroup>
            </DsFormGroup>

            <NotizDateTime
              v-model:date-from="dateFrom"
              v-model:date-to="dateTo"
              data-test="aktionen__notiz__date-time"
            />
          </span>
        </FormSection>
      </div>

      <FormSection title="Notiz">
        <NachrichtContent
          v-model="content"
          validation-name="attributes.content"
          label="Inhalt"
          data-test="aktionen__nachricht__content"
          required
        />

        <FormFiles
          v-model:uploaded-files="uploadedFiles"
          v-model:existing-documents="externalFiles"
          :kunde-id="kundeId"
          :gesellschaft-id="gesellschaftId"
          :vertraege-ids="vertraegeIds"
          :remove-file-from-existing-documents="removeFileFromExistingDocuments"
        />
      </FormSection>
    </div>

    <DsButton
      class="float-right w-52"
      icon="plus"
      :handler="save"
    >
      Gesprächsnotiz anlegen
    </DsButton>
  </DsForm>
</template>

<script setup lang="ts">
import {
  DsForm,
  DsSwitch,
  DsFormGroup,
  DsRadioGroup,
  DsRadioButton,
  DsButton,
} from '@demvsystems/design-components';
import { addMinutes } from 'date-fns';
import { ref } from 'vue';

import { extractErrors, isAxiosError, post } from '@/api';
import FormFiles from '@/components/form/FormFiles.vue';
import FormSection from '@/components/form/FormSection.vue';
import NachrichtContent from '@/components/form/NachrichtContent.vue';
import { useFiles } from '@/components/form/useFiles';
import { injectStore, useVorgangFromRoute } from '@/store/resources/composition';
import {
  GespraechsnotizResource,
  GespraechsType,
  VertragResource,
} from '@/store/resources/types';
import { toDEformatter } from '@/utils/dateTimeFormatter';

import NotizDateTime from './notizDateTime/NotizDateTime.vue';
import { reloadStoreFromCurrentVorgang } from './storeService';

const emit = defineEmits<{
  (event: 'resetNavigationStack'): void
}>();

const content = ref<string>('');

const hasDetails = ref(true);
const type = ref<GespraechsType>(GespraechsType.OnSite);
const dateFrom = ref(new Date());
const dateTo = ref(addMinutes(new Date(), 15));

const store = injectStore();
const vorgang = useVorgangFromRoute();

const kundeId = vorgang.value?.relationships?.kunde?.data?.id;
const gesellschaftId = vorgang.value?.relationships?.gesellschaft?.data?.id;
const vertraegeIds = store.vertraege.getAll().map(
  ({ id }: VertragResource) => id,
);

const {
  uploadedFiles,
  externalFiles,
  removeFileFromExistingDocuments,
  saveFiles,
} = useFiles();

const validationErrors = ref({});

const save = async () => {
  try {
    const { data: notiz } = await post<GespraechsnotizResource>(
      `/vorgaenge/${vorgang.value?.id ?? ''}/gespraechsnotizen`,
      {
        data: {
          type: 'gespraechsnotizen',
          attributes: {
            type: type.value,
            content: content.value,
            dateFrom: toDEformatter.format(dateFrom.value),
            dateTo: toDEformatter.format(dateTo.value),
          },
        },
      },
    );

    if (vorgang.value?.relationships?.timeline?.data === undefined) {
      return;
    }

    const ownerId = notiz.data?.id;
    const ownerType = notiz.data?.type;

    if (ownerId === undefined || ownerType === undefined) {
      return;
    }

    await saveFiles(ownerId, ownerType);

    await reloadStoreFromCurrentVorgang(store, vorgang.value.id);

    emit('resetNavigationStack');
  } catch (e) {
    if (!isAxiosError(e) || e.response === undefined) {
      throw e;
    }

    validationErrors.value = extractErrors(e.response.data?.errors ?? []);
  }
};
</script>
