<template>
  <DsButton
    variant="secondary"
    size="sm"
    data-test="vorlage-form__footer__preview"
    :disabled="isDisabled"
    @click="openVorlagenVorschauModal"
  >
    Zur Vorschau
  </DsButton>

  <DsModal
    :show="viewVorlagenVorschauModal"
    size="md"
    anchor="top"
    custom-content
    hide-buttons
  >
    <div
      class="flex flex-col"
      style="max-height: calc(100vh - 7rem)"
    >
      <DetailContentPreview
        :cc="ccProxy"
        :bcc="bccProxy"
        :subject="previewSubject"
        :empfaenger="empfaengerProxy"
        :versandart="versandart"
        :preview-pdf="previewPdf"
        :content="previewContent"
        :suppress-errors-and-retry="suppressErrorsAndRetry"
        :absender="[{email: 'Versender'}]"
        class="-mx-4 -mt-4 overflow-auto p-6 sm:-mx-6 sm:-mt-6"
      />
      <footer class="-mx-6 flex items-center justify-end space-x-4 border-t px-6 pt-6">
        <DsButton
          size="lg"
          data-test="vorlage-vorschau-modal__close"
          @click="viewVorlagenVorschauModal = false"
        >
          Schließen
        </DsButton>
      </footer>
    </div>
  </DsModal>
</template>

<script setup lang="ts">
import { DsButton, DsModal } from '@demvsystems/design-components';
import axios from 'axios';
import { computed, ref } from 'vue';

import DetailContentPreview from '@/components/DetailContentPreview.vue';
import { BriefAbsender, BriefEmpfaenger } from '@/pages/vorgangAnlegen/types';
import { Versandart, Vorlagenart } from '@/pages/vorlagen/types';
import { eventBus } from '@/store/resources/store';
import {
  EmailElement,
  VorlageMailEmpfaenger,
  VorlageMailEmpfaengerType,
} from '@/store/resources/types';
import { tags } from '@/store/tags';

const props = defineProps<{
  mailEmpfaenger: VorlageMailEmpfaenger[],
  cc: VorlageMailEmpfaenger[],
  bcc: VorlageMailEmpfaenger[],
  vorlagenart: Vorlagenart,
  versandart: Versandart,
  content: string | null,
  subject: string | null,
  briefEmpfaenger: BriefEmpfaenger,
  briefAbsender: BriefAbsender,
  suppressErrorsAndRetry: boolean,
}>();

const viewVorlagenVorschauModal = ref(false);
const previewPdf = ref<string | undefined>(undefined);
const previewContent = ref<string | undefined>(undefined);
const previewSubject = ref<string | undefined>(undefined);

async function getPdf() {
  previewPdf.value = undefined;
  try {
    const isVorgangsvorlage = props.vorlagenart === Vorlagenart.Vorgang;

    const response = await axios.post<string>('/api/vorlagen/preview/pdf/', {
      data: {
        type: 'previewPdf',
        attributes: {
          absender: isVorgangsvorlage ? props.briefAbsender : BriefAbsender.Makler,
          empfaenger: isVorgangsvorlage ? props.briefEmpfaenger : BriefEmpfaenger.Kunde,
          content: props.content,
          tags: tags.value,
        },
      },
    });

    if (response.status === 200) {
      viewVorlagenVorschauModal.value = true;
    }

    previewPdf.value = response.data;
  } catch (e) {
    eventBus.emit('error', 'Vorschau konnte nicht geladen werden.');
    throw new Error('Vorlagenvorschau PDF konnte nicht geladen werden.');
  }
}

async function getHtml() {
  previewContent.value = undefined;
  previewSubject.value = undefined;
  try {
    const response = await axios.post<{ subject: string, content: string }>('/api/vorlagen/preview/html/', {
      data: {
        type: 'previewHtml',
        attributes: {
          subject: props.subject,
          content: props.content,
          tags: tags.value,
        },
      },
    });

    if (response.status === 200) {
      viewVorlagenVorschauModal.value = true;
    }

    previewContent.value = response.data.content;
    previewSubject.value = response.data.subject;
  } catch (e) {
    eventBus.emit('error', 'Vorschau konnte nicht geladen werden.');
    throw new Error('Vorlagenvorschau HTML konnte nicht geladen werden.');
  }
}

async function openVorlagenVorschauModal() {
  if (props.versandart === Versandart.Brief) {
    await getPdf();

    return;
  }

  await getHtml();
}

function transformMailType(mailValue: string) {
  const mailTypeName = Object.keys(VorlageMailEmpfaengerType)[
    Object.values(VorlageMailEmpfaengerType).indexOf(mailValue as VorlageMailEmpfaengerType)
  ];

  return { email: mailTypeName ?? mailValue } as EmailElement;
}

const isDisabled = computed(() => {
  const noContent = props.content === null || props.content === '';
  const noSubject = props.versandart === Versandart.Mail
    && (props.subject === null || props.subject === '');

  return noContent || noSubject ;
});

const empfaengerProxy = computed(() => {
  if (props.vorlagenart === Vorlagenart.Kampagne) {
    return [];
  }

  return props.mailEmpfaenger.map((value) =>  transformMailType(value));
});

const ccProxy = computed(() => {
  if (props.vorlagenart === Vorlagenart.Kampagne) {
    return [];
  }

  return props.cc.map((value) =>  transformMailType(value));
});

const bccProxy = computed(() => {
  if (props.vorlagenart === Vorlagenart.Kampagne) {
    return [];
  }

  return props.bcc.map((value) =>  transformMailType(value));
});
</script>
