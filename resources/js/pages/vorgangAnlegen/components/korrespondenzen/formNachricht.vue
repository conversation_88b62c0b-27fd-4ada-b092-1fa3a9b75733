<template>
  <div class="space-y-3">
    <h3 class="text-base font-semibold leading-none">
      Nachricht
    </h3>

    <DsFormGroup
      v-if="vorgangAnlegenStore.versandart === Versandart.Mail"
      label="Betreff"
      required
      validation-name="attributes.vorgangsart.attributes.betreff"
      data-test="vorgang-anlegen__nachricht__betreff"
    >
      <div
        class="flex flex-col gap-x-4 gap-y-3 sm:flex-row"
      >
        <DsEditor
          :model-value="vorgangAnlegenStore.betreff"
          :extensions="getEditorExtensions(true)"
          class="flex grow"
          inline
          html
          @update:model-value="sanitizeAndUpdate('betreff', $event as string)"
          @focus="focus"
          @blur="blur"
        />
        <DsEditor
          :model-value="vorgangsnummer"
          class="shrink-0 md:self-center"
          data-test="vorgang-anlegen__nachricht__vorgangsnummer"
          title="Die Vorgangsnummer wird ihrem E-Mail-Betreff angehangen, um etwaige Antworten diesem Vorgang zuweisen zu können"
          disabled
          inline
        />
      </div>
    </DsFormGroup>

    <DsFormGroup
      v-else
      class="w-32"
      label="Datum"
    >
      <DsInput
        v-model="briefDatumProxy"
        placeholder="TT.MM.JJJJ"
        data-test="brief-datum"
        type="date"
      />
    </DsFormGroup>

    <div class="space-y-2">
      <DsFormGroup
        label="Inhalt"
        required
        validation-name="attributes.vorgangsart.attributes.content"
        data-test="vorgang-anlegen__nachricht__inhalt"
      >
        <DsEditor
          :model-value="vorgangAnlegenStore.content"
          :extensions="getEditorExtensions()"
          :image-upload-handler="imageUploadHandler"
          :images="vorgangAnlegenStore.versandart === Versandart.Mail"
          :is-loading="isLoadingTextGeneration"
          class="w-full"
          font-family
          font-size
          text-color
          highlight
          text-alignment
          links
          history
          html
          style="min-height: 16rem; font-family: arial, helvetica, sans-serif; font-size: 12pt; white-space: break-spaces"
          @update:model-value="sanitizeAndUpdate('content', $event as string)"
          @focus="focus"
          @blur="blur"
        />
      </DsFormGroup>

      <div class="flex justify-end space-x-2">
        <TextGeneration
          v-if="user !== undefined && user.attributes?.canUseTextGeneration"
          :location="TextGenerationLocation.VorgangAnlegen"
          :content="vorgangAnlegenStore.content"
          :anrede="vorgangAnlegenStore.anrede"
          :versandart="vorgangAnlegenStore.versandart"
          :vorgangstyp-id="vorgangAnlegenStore.vorgangstypId"
          :sparte-id="vorgangAnlegenStore.sparteId"
          @update:content="vorgangAnlegenStore.content = $event"
        />
        <PlatzhalterInfoBox />
        <VorlageAusVorgangModal :disabled="vorlageSpeichernDisabled" />
      </div>
    </div>
    <FormFiles
      v-if="vorgangAnlegenStore.versandart === 'email'"
      v-model:uploaded-files="vorgangAnlegenStore.uploadedFiles"
      v-model:existing-documents="existingFilesProxy"
      :kunde-id="vorgangAnlegenStore.kundeId ?? undefined"
      :gesellschaft-id="vorgangAnlegenStore.gesellschaftId ?? undefined"
      :vertraege-ids="vertraegeIds"
      :vorgang-id="vorgangAnlegenStore.vorgaengerId ?? undefined"
      :remove-file-from-existing-documents="vorgangAnlegenStore.removeFileFromExistingDocuments"
      :vorlage-attachments="vorlageAttachments"
      :sparte-id="vorgangAnlegenStore.sparte?.id ?? undefined"
      :vorgangstyp-id="vorgangAnlegenStore.vorgangstypId ?? undefined"
      :kundendokument-ids="vorgangAnlegenStore.kundendokumentIds"
      show-total-file-size-alerts
    />
  </div>
</template>

<script setup lang="ts">
import { DsEditor, DsFormGroup, DsInput } from '@demvsystems/design-components';
import { format } from 'date-fns';
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref, watch } from 'vue';

import PlatzhalterInfoBox from '@/components/PlatzhalterInfoBox.vue';
import { getEditorExtensions } from '@/components/extensions/tags/getEditorExtensions';
import FormFiles from '@/components/form/FormFiles.vue';
import { emptyHtmlToEmptyString } from '@/components/form/utils/emptyHtmlToEmptyString';
import TextGeneration from '@/components/textGeneration/TextGeneration.vue';
import { TextGenerationLocation } from '@/components/textGeneration/types';
import useCurrentUser from '@/components/users/useCurrentUser';
import { useImageUploadHandler } from '@/composables/useImageUploadHandler';
import { useTextGeneration } from '@/composables/useTextGeneration';
import { Versandart } from '@/pages/vorlagen/types';
import { ExternalFileResource, VertragResource } from '@/store/resources/types';

import { useVorgangAnlegenStore } from '../../stores/vorgangAnlegenStore/vorgangAnlegenStore';

import VorlageAusVorgangModal from './vorlageAusVorgang/VorlageAusVorgangModal.vue';

const vorgangAnlegenStore = useVorgangAnlegenStore();
const { firstTimelineElementId, firstTimelineElementType } = storeToRefs(vorgangAnlegenStore);

const { firma, user } = useCurrentUser();
const { isLoading: isLoadingTextGeneration } = useTextGeneration();
const { imageUploadHandler } = useImageUploadHandler(
  firstTimelineElementId,
  firstTimelineElementType,
);

// Allow to set briefdatum to undefined
const briefDatumProxy = computed<Date | undefined>({
  get: () => {
    if (vorgangAnlegenStore.briefDatum === undefined) {
      return undefined;
    }

    return new Date(vorgangAnlegenStore.briefDatum);
  },

  set: (newVal: Date | undefined | null) => {
    if (newVal === undefined || newVal === null) {
      vorgangAnlegenStore.briefDatum = undefined;
    } else {
      vorgangAnlegenStore.briefDatum = format(newVal, 'yyyy-MM-dd');
    }
  },
});

const existingFilesProxy = computed<ExternalFileResource[]>({
  get: () => vorgangAnlegenStore.externalFiles,
  set: (newVal: ExternalFileResource[]) => {
    vorgangAnlegenStore.externalFiles = newVal.map((newExternalFile) => {
      const existingFile = vorgangAnlegenStore.externalFiles.find((externalFile) => (
        externalFile.attributes.url === newExternalFile.attributes.url
      ));

      return existingFile ?? newExternalFile;
    });
  },
});

const vorgangsnummer = computed<string>(() => {
  return vorgangAnlegenStore.vorgangsnummer ?? `${firma.value?.attributes.kuerzel ?? ''}-000000`;
});

onMounted(() => {
  briefDatumProxy.value = new Date();
});

watch(() => vorgangAnlegenStore.versandart, () => {
  if (vorgangAnlegenStore.versandart === Versandart.Brief) {
    briefDatumProxy.value = new Date();
    vorgangAnlegenStore.deleteSavedFiles();

    return;
  }

  delete vorgangAnlegenStore.briefDatum;
});

const vertraegeIds = computed(
  () => vorgangAnlegenStore.vertraege.map(
    ({ id }: VertragResource) => id,
  ),
);

const vorlageAttachments = computed(
  () => vorgangAnlegenStore.selectedVorlageMail?.attributes.attachments,
);

const vorlageSpeichernDisabled = computed(() => (
  vorgangAnlegenStore.ownerId !== undefined
));

const isFocused = ref(false);

function toggleContentEdited() {
  if (isFocused.value) {
    vorgangAnlegenStore.contentOrBetreffWasEdited = true;
  }
}

function sanitizeAndUpdate(key: 'betreff' | 'content', value: string) {
  vorgangAnlegenStore[key] = emptyHtmlToEmptyString(value);
  toggleContentEdited();
}

function focus() {
  isFocused.value = true;
}

function blur() {
  isFocused.value = false;
}
</script>
