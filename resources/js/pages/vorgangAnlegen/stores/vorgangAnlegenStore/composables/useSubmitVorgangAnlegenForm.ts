import axios, { AxiosResponse } from 'axios';
import { format } from 'date-fns';
import { computed } from 'vue';

import { extractErrors, isAxiosError, post, put } from '@/api';
import { useImageUploadHandler } from '@/composables/useImageUploadHandler';
import { Versandart } from '@/pages/vorlagen/types';
import { eventBus } from '@/store/resources/store';
import { FirstTimelineElementResource, VorgangResource } from '@/store/resources/types';
import { VorgangStatus } from '@/types';
import { Document } from '@/types/jsonapi';

import { useVorgangAnlegenStore } from '../vorgangAnlegenStore';

/**
 * This composable contains the logic for submitting the vorgangAnlegenStore,
 * including building the request and handling the backend response.
 */
export const useSubmitVorgangAnlegenForm = (): {
  submit: () => Promise<string>,
  submitAsEntwurf: () => Promise<void>,
} => {
  const vorgangAnlegenStore = useVorgangAnlegenStore();

  // use computed here because storeToRefs is not reactive here because of nesting:
  // the store imports the composable, which imports the store
  const imageOwnerId = computed(() => vorgangAnlegenStore.firstTimelineElementId);
  const imageOwnerType = computed(() => vorgangAnlegenStore.firstTimelineElementType);
  const {
    uploadImagesFromBlobUrls,
    hasBlobUrls,
  } = useImageUploadHandler(imageOwnerId, imageOwnerType);

  function getKorrespondenzResource(): FirstTimelineElementResource {
    const resource = <FirstTimelineElementResource>{
      type: 'korrespondenzen',
      id: vorgangAnlegenStore.firstTimelineElementId ?? '',
      attributes: {
        betreff: vorgangAnlegenStore.betreff,
        content: vorgangAnlegenStore.content,
        versandart: vorgangAnlegenStore.versandart,
        empfaenger: vorgangAnlegenStore.empfaenger,
        cc: vorgangAnlegenStore.mailCc,
        bcc: vorgangAnlegenStore.mailBcc,
      },
    };

    if (vorgangAnlegenStore.versandart === Versandart.Brief) {
      resource.attributes.briefDatum = vorgangAnlegenStore.briefDatum;
      resource.attributes.briefAbsenderType = vorgangAnlegenStore.briefAbsender;
    }

    return resource;
  }

  function getKommentarResource(): FirstTimelineElementResource {
    return {
      type: 'kommentare',
      id: vorgangAnlegenStore.firstTimelineElementId ?? '',
      attributes: {
        content: vorgangAnlegenStore.content,
      },
    };
  }

  function getFirstTimelineElementResource(): FirstTimelineElementResource {
    switch (vorgangAnlegenStore.firstTimelineElementType) {
      case 'korrespondenzen':
        return getKorrespondenzResource();
      case 'kommentare':
        return getKommentarResource();
      default:
        throw Error('Not a valid vorgangsart!');
    }
  }

  function buildRequest() {
    return {
      data: {
        type: 'vorgaenge',
        attributes: {
          titel: vorgangAnlegenStore.vorgangstitel,
          faelligAt: vorgangAnlegenStore.faelligAt !== null ? format(vorgangAnlegenStore.faelligAt, 'yyyy-MM-dd') : null,
          vorgangsart: getFirstTimelineElementResource(),
          ...(vorgangAnlegenStore.bezug !== null && {
            bezug: vorgangAnlegenStore.bezug,
          }),
          status: VorgangStatus.Entwurf,
        },
        relationships: {
          owner: {
            ...(vorgangAnlegenStore.ownerId != undefined ? {
              data: {
                type: 'users',
                id: vorgangAnlegenStore.ownerId,
              },
            } : {
              data: null,
            }),
          },
          ...(vorgangAnlegenStore.vorgaengerId != undefined ? {
            vorgaenger: {
              data: {
                type: 'vorgaenge',
                id: vorgangAnlegenStore.vorgaengerId,
              },
            },
          } : {}),
          ...(vorgangAnlegenStore.gesellschaftId != undefined ? {
            gesellschaft: {
              data: {
                type: 'gesellschaften',
                id: vorgangAnlegenStore.gesellschaftId,
              },
            },
          } : {}),
          ...(vorgangAnlegenStore.vertriebswegIdProxy != null ? {
            vertriebsweg: {
              data: {
                type: 'vertriebsweg',
                id: vorgangAnlegenStore.vertriebswegIdProxy,
              },
            },
          } : {}),
          ...(vorgangAnlegenStore.kundeId != undefined ? {
            kunde: {
              data: {
                type: 'kunden',
                id: vorgangAnlegenStore.kundeId,
              },
            },
          } : {}),
          ...(vorgangAnlegenStore.vorgangstypId != undefined
            && vorgangAnlegenStore.firstTimelineElementType === 'korrespondenzen' ? {
              vorgangTyp: {
                data: {
                  type: 'vorgangTypen',
                  id: vorgangAnlegenStore.vorgangstypId,
                },
              },
            } : {}),
          ...(vorgangAnlegenStore.sparteId != undefined ? {
            sparte: {
              data: {
                type: 'sparten',
                id: vorgangAnlegenStore.sparteId,
              },
            },
          } : {}),
          ...(vorgangAnlegenStore.vertraege.length > 0 ? {
            vertraege: {
              data: vorgangAnlegenStore.vertraege.map((vertrag) => ({
                type: 'vertraege',
                id: vertrag.id,
              })),
            },
          } : {}),
          ...(vorgangAnlegenStore.beobachterIds.length > 0
            || vorgangAnlegenStore.bearbeiterIds.length > 0  ? {
              participants: {
                data: vorgangAnlegenStore.beobachterIds.map((beobachter) => ({
                  type: 'vorgang_participant',
                  attributes: {
                    participant_type: 'beobachter',
                    user_id: beobachter,
                  },
                })).concat(vorgangAnlegenStore.bearbeiterIds.map((bearbeiter) => ({
                  type: 'vorgang_participant',
                  attributes: {
                    participant_type: 'bearbeiter',
                    user_id: bearbeiter,
                  },
                }))),
              },
            } : {}),
        },
      },
    };
  }

  async function handleRequest<R extends AxiosResponse<Document>>(
    request: Promise<R>,
    prefix?: string,
  ): Promise<R | undefined> {
    try {
      const response = await request;
      vorgangAnlegenStore.errors = {};

      if (response.data.data === undefined) {
        eventBus.emit('error');

        return undefined;
      }

      return response;
    } catch (e) {
      if (!isAxiosError(e) || e.response === undefined || e.response.status >= 500) {
        eventBus.emit('error');
        throw e;
      }

      vorgangAnlegenStore.errors = extractErrors(
        e.response.data.errors ?? [],
        prefix,
      );

      return undefined;
    }
  }

  async function submitAsEntwurf(): Promise<void> {
    const data = buildRequest();

    const response = await handleRequest(
      vorgangAnlegenStore.id === undefined
        ? post<VorgangResource>('vorgaenge/', data)
        : put<VorgangResource>(`vorgaenge/${vorgangAnlegenStore.id}`, data),
    );

    await updateFormFromResponse(response);
    await vorgangAnlegenStore.saveFiles();

    if (hasBlobUrls(vorgangAnlegenStore.content)) {
      vorgangAnlegenStore.content = await uploadImagesFromBlobUrls(vorgangAnlegenStore.content);
      const updateContentResponse = await handleRequest(
        put<VorgangResource>(`vorgaenge/${vorgangAnlegenStore.id}`, {
          data: {
            type: 'vorgaenge',
            attributes: {
              vorgangsart: getFirstTimelineElementResource(),
            },
          },
        }),
      );

      await updateFormFromResponse(updateContentResponse);
    }
  }

  async function updateFormFromResponse(
    response: AxiosResponse<Document<VorgangResource>> | undefined,
  ): Promise<void> {
    if (response?.data.data === undefined) {
      throw new Error('No response while saving new Vorgang ');
    }

    vorgangAnlegenStore.id = response.data.data.id;
    vorgangAnlegenStore.vorgangsnummer = (
      response.data.data.attributes.vorgangsnummer
    );

    const firstTimelineElement = response.data.included?.find((resource) => (
      resource.type === vorgangAnlegenStore.firstTimelineElementType
    )) as FirstTimelineElementResource;

    if (firstTimelineElement === undefined) {
      throw Error('No root timeline element found!');
    }

    if (
      firstTimelineElement.links?.content !== undefined
      && firstTimelineElement.attributes.versandart !== 'brief'
    ) {
      const contentResponse = await axios.get<{ content: string }>(
        firstTimelineElement.links.content,
      );
      vorgangAnlegenStore.renderedContent = contentResponse.data.content;
    }

    vorgangAnlegenStore.firstTimelineElementId = firstTimelineElement.id;
    vorgangAnlegenStore.renderedBetreff = firstTimelineElement.attributes.betreff;
    if (firstTimelineElement.attributes.versandart === 'email') {
      vorgangAnlegenStore.mailOwnerSenderAddress = firstTimelineElement.attributes.absender;
    }
  }

  async function submit(): Promise<string> {
    if (vorgangAnlegenStore.id === undefined) {
      await submitAsEntwurf();
    }

    const isAufgabe = vorgangAnlegenStore.firstTimelineElementType === 'kommentare';

    const response = await put<VorgangResource>(
      `vorgaenge/${vorgangAnlegenStore.id}`,
      {
        data: {
          type: 'vorgaenge',
          attributes: {
            // We don't want Vorgange visible without Fälligkeit
            status: isAufgabe || vorgangAnlegenStore.faelligAt !== null
              ? VorgangStatus.Offen
              : VorgangStatus.Erledigt,
          },
        },
      },
    );

    const data = response?.data.data;
    if (data === undefined) {
      throw new Error(`No response, put vorgaenge/${vorgangAnlegenStore.id}`);
    }

    vorgangAnlegenStore.reset();

    return data.attributes.vorgangsnummer;
  }

  return {
    submit,
    submitAsEntwurf,
  };
};
