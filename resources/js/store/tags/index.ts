import { captureException } from '@sentry/vue';
import { asyncComputed } from '@vueuse/core';
import { ref } from 'vue';

import { get } from '../../api';

import { TagDescription } from './types';

const tagList = ref<TagDescription[]>([]);
const hasRequested = ref(false);

export const fetchTags = async (): Promise<void> => {
  try {
    const { data } = await get<TagDescription[]>('tags');
    tagList.value = data.data ?? [];
  } catch (e) {
    captureException('Error while fetching tags. store/tags/index.ts');
  }
};

// Export read-only list of tags
export const tags = asyncComputed(async () => {
  if (!hasRequested.value) {
    hasRequested.value = true;
    await fetchTags();
  }

  return tagList.value;
}, []);
