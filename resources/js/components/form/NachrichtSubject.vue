<template>
  <DsFormGroup
    :label="label"
    :required="required"
    :validation-name="validationName"
  >
    <DsEditor
      v-model="model"
      :disabled="disabled"
      :extensions="getEditorExtensions(true)"
      inline
      html
    />
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsEditor, DsFormGroup } from '@demvsystems/design-components';

import { getEditorExtensions } from '@/components/extensions/tags/getEditorExtensions';

import { emptyHtmlToEmptyString } from './utils/emptyHtmlToEmptyString';

const model = defineModel<string | null>({
  required: true,
  set: (v) => emptyHtmlToEmptyString(v),
});

withDefaults(defineProps<{
  modelValue: string | null,
  validationName?: string,
  label?: string,
  required?: boolean,
  disabled?: boolean,
}>(), {
  label: 'Betreff',
  validationName: undefined,
});
</script>
