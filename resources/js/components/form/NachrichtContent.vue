<template>
  <DsFormGroup
    :label="label"
    :required="required"
    :validation-name="validationName"
  >
    <DsEditor
      v-model="model"
      :disabled="disabled"
      :extensions="getEditorExtensions()"
      :images="imageUploadHandler !== undefined"
      :image-upload-handler="imageUploadHandler"
      :is-loading="isLoading"
      font-family
      font-size
      text-color
      highlight
      text-alignment
      links
      history
      html
      style="min-height: 16rem; font-family: arial, helvetica, sans-serif; font-size: 12pt;"
    />
    <div class="flex flex-row-reverse gap-3">
      <PlatzhalterInfoBox />
      <slot />
    </div>
  </DsFormGroup>
</template>

<script setup lang="ts">
import { DsEditor, DsFormGroup } from '@demvsystems/design-components';

import { getEditorExtensions } from '@/components/extensions/tags/getEditorExtensions';

import PlatzhalterInfoBox from '../PlatzhalterInfoBox.vue';

import { emptyHtmlToEmptyString } from './utils/emptyHtmlToEmptyString';

const model = defineModel<string | null>({
  required: true,
  set: (v) => emptyHtmlToEmptyString(v),
});

withDefaults(defineProps<{
  modelValue: string | null,
  validationName?: string,
  label?: string,
  imageUploadHandler?: (files: File[]) => Promise<string>[],
  required?: boolean,
  disabled?: boolean,
  isLoading?: boolean,
}>(), {
  label: 'Inhalt',
  validationName: undefined,
  imageUploadHandler: undefined,
});
</script>
