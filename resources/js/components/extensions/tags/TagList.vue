<template>
  <DsDropdownBase
    ref="dropdown"
    :items="itemTags"
    :width="350"
    preselect-first-value
    @select="selectItem"
  >
    <template #item="{item}">
      <div class="line-clamp-3 px-2 text-xs">
        <span
          class="block font-mono font-bold text-blue-700"
          v-text="item.attributes.tag"
        />
        <span
          class="block leading-tight"
          v-text="item.attributes.description"
        />
        <span
          v-if="item.attributes.example.length > 0"
          class="block text-gray-700"
          v-html="item.attributes.example"
        />
      </div>
    </template>

    <template #no-items>
      <div class="p-2 text-sm text-gray-500">
        Keine Platzhalter gefunden
      </div>
    </template>
  </DsDropdownBase>
</template>

<script setup lang="ts">
import { DsDropdownBase } from '@demvsystems/design-components';
import type { SuggestionProps } from '@tiptap/suggestion';
import { ref, computed } from 'vue';

import type { TagDescription } from '@/store/tags/types';

const props = defineProps<{
  items: string[];
  command: SuggestionProps['command'];
  tags: TagDescription[];
}>();

const selectItem = (index: number) => {
  const item = props.items[index];
  if (item) {
    props.command({ name: item });
  }
};

const itemTags = computed(() =>
  props.items
    .map((name) => props.tags.find((tag) => tag.attributes.tag === name))
    .filter((tag): tag is TagDescription => tag !== undefined),
);

const dropdown = ref<{
  selectNext: () => void;
  selectPrevious: () => void;
  emitSelectedIndex: () => void;
  select: (index: number) => void;
} | null>(null);

const onKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'ArrowDown') {
    dropdown.value?.selectNext();

    return true;
  }

  if (e.key === 'ArrowUp') {
    dropdown.value?.selectPrevious();

    return true;
  }

  if (e.key === 'Enter') {
    dropdown.value?.emitSelectedIndex();

    return true;
  }

  dropdown.value?.select(0);

  return false;
};

defineExpose({ onKeyDown });
</script>
