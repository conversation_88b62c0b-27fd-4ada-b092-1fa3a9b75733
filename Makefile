#!make

##
## TEST COMMANDS
## -------------
##

.PHONY: lint lint-fix test

lint: ## Lint Project
	./run.sh vendor/bin/phpcstd

lint-fix: ## Lint Project and fix fixable issues
	./run.sh vendor/bin/phpcstd --fix

test: ## Run Unit-Tests
	./run.sh vendor/bin/phpunit

##
## Composer
## --------
##
.PHONY: install update

install: ## install composer dependencies
	./run.sh composer install

update: ## update composer dependencies
	./run.sh composer update

##
## HELP
## ----
##

.PHONY: help
.DEFAULT_GOAL := help

help: ## show this help message
	@grep -h -E '(^[a-zA-Z0-9_-]+:.*?##.*$$)|(^##)' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[32m%-30s\033[0m %s\n", $$1, $$2}' | sed -e 's/\[32m##/[33m/'
