<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk;

use Demv\ProfessionalworksSdk\ApplicationAuthToken\ApplicationAuthTokenService;
use Demv\ProfessionalworksSdk\Gateway\ProfessionalworksGateway;
use Demv\ProfessionalworksSdk\OAuth\OAuthService;
use Demv\SdkFramework\Gateway\GatewayInterface;
use Illuminate\Support\ServiceProvider;

class ProfessionalWorksSDKProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->publishes([
            __DIR__ . '/../config/professionalworks_sdk.php' => config_path('professionalworks_sdk.php'),
        ]);
    }

    public function register(): void
    {
        $this->mergeConfigFrom(
            __DIR__ . '/../config/professionalworks_sdk.php',
            config_path('professionalworks_sdk.php'),
        );

        $this->app->singleton(GatewayInterface::class, function (): ProfessionalworksGateway {
            $gatewayConfig = $this->app->get('config')->get('professionalworks_sdk');
            $options = [];

            //Schema: '['professionalworks.local:80:127.0.0.1']'
            if (isset($gatewayConfig['hosts'])) {
                $options['curl'][CURLOPT_RESOLVE] = $gatewayConfig['hosts'];
            }

            return new ProfessionalworksGateway(
                (string) $gatewayConfig['baseUrl'],
                (string) $gatewayConfig['token'],
                (string) $gatewayConfig['origin'],
                (string) $gatewayConfig['proxy']['user'] ?? '',
                (string) $gatewayConfig['proxy']['password'] ?? '',
                (string) $gatewayConfig['proxy']['baseUrl'] ?? '',
                $options,
            );
        });

        $this->app->singleton(OAuthService::class, function (): OAuthService {
            $professionalworksGateway = $this->app->get(GatewayInterface::class);

            return new OAuthService(
                (string) $this->app->get('config')->get('professionalworks_sdk.oauth.publicKey'),
                $professionalworksGateway,
                (string) $this->app->get('config')->get('professionalworks_sdk.oauth.loginUrl'),
            );
        });

        $this->app->singleton(ApplicationAuthTokenService::class, function (): ApplicationAuthTokenService {
            $professionalworksGateway = $this->app->get(GatewayInterface::class);

            return new ApplicationAuthTokenService(
                (string) $this->app->get('config')->get('professionalworks_sdk.oauth.publicKey'),
                $professionalworksGateway,
            );
        });
    }
}
