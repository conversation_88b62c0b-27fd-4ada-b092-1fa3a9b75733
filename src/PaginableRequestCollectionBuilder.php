<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk;

use Demv\SdkFramework\Gateway\ExpectJsendResponseMiddleware;
use Demv\SdkFramework\Gateway\GatewayInterface;
use Generator;
use Illuminate\Support\LazyCollection;

class PaginableRequestCollectionBuilder
{
    /**
     * @template T
     *
     * @param callable(array):T $jsonToDtoConverter
     * @param array<string, mixed> $params
     * @param callable(array, array|null):array $updateParams
     *
     * @return LazyCollection<int, T>
     */
    public static function build(
        string $url,
        array $params,
        GatewayInterface $gateway,
        callable $updateParams,
        callable $jsonToDtoConverter,
        int $pageLimit = 0,
    ): LazyCollection {
        $collection = LazyCollection::make(static function () use ($url, $params, $gateway, $updateParams): Generator {
            do {
                //TODO: We could implement a minimum time to expire before running the next request
                $response = $gateway->get(
                    uri: $url,
                    params: $params,
                    middlewares: [
                        new ExpectJsendResponseMiddleware(),
                    ],
                    throw: true,
                );
                $decodedResponse = json_decode(
                    json: $response->getBody()->getContents(),
                    associative: true,
                    flags: JSON_THROW_ON_ERROR,
                );

                $data = array_values($decodedResponse['data']['data']);
                $last = last($data);
                $params = $updateParams($params, is_array($last) ? $last : null);

                yield $data;
            } while (count($data) === $params['limit']);
        });

        if ($pageLimit > 0) {
            $collection = $collection->take($pageLimit);
        }

        return $collection
            ->flatten(1)
            ->map($jsonToDtoConverter);
    }
}
