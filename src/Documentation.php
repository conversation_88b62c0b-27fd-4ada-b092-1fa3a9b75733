<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk;

use Demv\ProfessionalworksSdk\Documentation\DocumentationGatewayFactory;
use Demv\SdkFramework\Documentation\DocumentationGatewayFactoryInterface;
use Demv\SdkFramework\Documentation\DocumentationInterface;
use Demv\SdkFramework\Endpoint\EndpointInterface;

class Documentation implements DocumentationInterface
{
    /**
     * @return class-string<EndpointInterface>[]
     */
    public function getEndpoints(): array
    {
        return [
            Endpoints\Agency\AgencyEndpoint::class,
            Endpoints\Bestandsdaten\CountryEndpoint::class,
            Endpoints\Bestandsdaten\DocumentTypeEndpoint::class,
            Endpoints\Bestandsdaten\KundenDocumentsEndpoint::class,
            Endpoints\Bestandsdaten\KundenDetailsEndpoint::class,
            Endpoints\Bestandsdaten\KundenEndpoint::class,
            Endpoints\Bestandsdaten\VertraegeEndpoint::class,
            Endpoints\Bestandsdaten\SchaedenEndpoint::class,
            Endpoints\Bestandsdaten\KundenDocumentsEndpoint::class,
            Endpoints\Brief\BriefVorlagenEndpoint::class,
            Endpoints\Gesellschaft\AddressEndpoint::class,
            Endpoints\Gesellschaft\AnsprechpartnerEndpoint::class,
            Endpoints\Gesellschaft\GesellschaftenEndpoint::class,
            Endpoints\Gesellschaft\LogoEndpoint::class,
            Endpoints\Mail\MailAddressEndpoint::class,
            Endpoints\Mail\MailVorlagenEndpoint::class,
            Endpoints\Mail\SendMailEndpoint::class,
            Endpoints\Profile\BriefeinstellungenEndpoint::class,
            Endpoints\Sparten\SpartenEndpoint::class,
            Endpoints\Tags\TagsEndpoint::class,
            Endpoints\User\LogoEndpoint::class,
            Endpoints\User\ParentsEndpoint::class,
            Endpoints\User\RightsEndpoint::class,
            Endpoints\User\UnderlingsEndpoint::class,
            Endpoints\User\UserByFirmaEndpoint::class,
            Endpoints\User\UserEndpoint::class,
            Endpoints\Sync\StatusEndpoint::class,
            Endpoints\Duplikate\DuplikateEndpoint::class,
            Endpoints\Bedarfinfo\DokumentEndpoint::class,
            Endpoints\Bedarfinfo\LinkHistoryEndpoint::class,
            Endpoints\Bedarfinfo\ConfigEndpoint::class,
            Endpoints\Bedarfinfo\VersorgungEndpoint::class,
        ];
    }

    public function getName(): string
    {
        return 'Professional works';
    }

    public function getDescription(): string
    {
        return 'Professional works Endpunkte';
    }

    public function getGatewayFactory(): DocumentationGatewayFactoryInterface
    {
        return new DocumentationGatewayFactory();
    }
}
