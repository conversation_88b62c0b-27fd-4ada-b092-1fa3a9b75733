<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Documentation;

use Demv\ProfessionalworksSdk\Gateway\ProfessionalworksGateway;
use Demv\SdkFramework\Documentation\DocumentationGatewayFactoryInterface;
use Exception;

class DocumentationGatewayFactory implements DocumentationGatewayFactoryInterface
{
    private const NOT_ALLOWED_HOSTS = [
        'professional.works',
        'crm.deutscher-maklerverbund.de',
    ];

    public static function create(?string $baseUrl = null, ?string $pwApiToken = null): ProfessionalworksGateway
    {
        $baseUrl = $baseUrl ?? $_ENV['PW_BASE_URL'] ?? 'http://professionalworks.local';
        $urlData = parse_url($baseUrl);

        if (!is_array($urlData) || !isset($urlData['host'])) {
            throw new Exception('Invalid base url');
        }

        if (in_array($urlData['host'], self::NOT_ALLOWED_HOSTS, true)) {
            throw new Exception('You are not allowed to test against the Live Server (' . $baseUrl . ')');
        }

        return new ProfessionalworksGateway(
            baseUrl: $baseUrl ?? 'http://professionalworks.local',
            token: $pwApiToken ?? $_ENV['PW_API_TOKEN'] ?? '',
            origin: 'pw-sdk',
        );
    }
}
