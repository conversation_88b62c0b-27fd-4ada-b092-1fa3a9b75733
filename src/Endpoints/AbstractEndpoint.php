<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints;

use Demv\SdkFramework\Endpoint\EndpointInterface;
use Demv\SdkFramework\Gateway\ExpectJsendResponseMiddleware;
use Demv\SdkFramework\Gateway\GatewayInterface;
use Psr\Http\Message\ResponseInterface;

abstract class AbstractEndpoint implements EndpointInterface
{
    public function __construct(
        protected GatewayInterface $gateway,
    ) {
    }

    protected function getJsendResponse(string $uri, array $params): ResponseInterface
    {
        // todo: Retry-Middleware verwenden um temporäre Fehler abzufangen, bspw. Gateway Timeout.
        // todo: Rate Limiting, wir sollten nicht zu viele Requests gegen pw auf einmal abfeuern sollen.
        return $this->gateway->get(
            uri: $uri,
            params: $params,
            middlewares: [
                new ExpectJsendResponseMiddleware(),
            ],
            throw: true,
        );
    }

    protected function getData(string $url, array $params = []): array
    {
        return $this->responseAsJson($this->getJsendResponse($url, $params))['data'];
    }

    protected function responseAsJson(ResponseInterface $response): array
    {
        return json_decode(
            json: $response->getBody()->getContents(),
            associative: true,
        );
    }

    protected function createGetItemsByUrlFn(string $url, array $params = [], bool $includeKey = false): \Closure
    {
        return function () use ($url, $params, $includeKey): \Generator {
            return $this->fetchItems($url, $params, $includeKey);
        };
    }

    protected function fetchItems(string $url, array $params, bool $includeKey = false): \Generator
    {
        $response = $this->getData($url, $params);

        foreach ($response as $key => $item) {
            yield $includeKey ? [
                'key' => $key,
                'value' => $item,
            ] : $item;
        }
    }
}
