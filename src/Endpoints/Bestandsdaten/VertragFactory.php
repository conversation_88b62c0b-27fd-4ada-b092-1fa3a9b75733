<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Carbon\Carbon;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Vertrag;

class VertragFactory
{
    public static function convert(array $datenArray): Vertrag
    {
        return new Vertrag(
            id: (int) $datenArray['vertragId'],
            vertragsnummer: $datenArray['vertragsnummer'] ?? '',
            sparteId: (int) $datenArray['sparteId'],
            userId: (int) $datenArray['userId'],
            vertriebswegId: (int) $datenArray['vertriebswegId'],
            kundeId: (int) $datenArray['kundeId'],
            updatedAt: new Carbon($datenArray['updatedAt']),
            status: $datenArray['status'] !== null ? (int) $datenArray['status'] : null,
            agencyId: $datenArray['agencyId'] !== null ? (int) $datenArray['agencyId'] : null,
            kfzKennzeichen: $datenArray['kfzKennzeichen'] !== '' ? $datenArray['kfzKennzeichen'] : null,
            isDeleted: isset($datenArray['isDeleted']) ? (bool) $datenArray['isDeleted'] : null,
            mitversicherteKundenIds: json_decode($datenArray['mitversicherteKundenIds']) ?? null,
            gesellschaftId: $datenArray['gesellschaftId'] !== null ? (int) $datenArray['gesellschaftId'] : null,
            verwahrstelle: $datenArray['verwahrstelle'] ?? null,
        );
    }
}
