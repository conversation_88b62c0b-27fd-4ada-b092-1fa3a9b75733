<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Carbon\Carbon;
use Closure;
use Demv\ProfessionalworksSdk\Endpoints\AbstractEndpoint;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Schaden;
use Demv\ProfessionalworksSdk\PaginableRequestCollectionBuilder;
use Demv\SdkFramework\Attributes\Description;
use Demv\SdkFramework\Attributes\EndpointDescription;
use Demv\SdkFramework\Attributes\ParameterDescription;
use Illuminate\Support\LazyCollection;

#[EndpointDescription(
    description: 'Schaden-Bestandsdaten - Ein reduziertes Datenset der Verträge.',
    longDescription: 'Die Daten dienen der Synchronisierung der Basisdaten eines Schadens. ' .
    'Genutzt werden diese Daten beispielsweise in den Vorgängen um Vorgänge mit Verträgen verknüpfen zu können',
)]
class SchaedenEndpoint extends AbstractEndpoint
{
    private const URL_INDEX = '/api/bestandsdaten/schaeden';
    private const URL_FIRMA_INDEX = '/api/bestandsdaten/firma/%s/schaeden';
    private const URL_SINGLE_INDEX = '/api/bestandsdaten/schaeden/%s';

    /**
     * @return LazyCollection<int, Schaden>
     */
    #[Description(description: 'Gibt eine bestimmte Anzahl der Schäden zu einer Firma zurück. Eingeschränkt durch die Zugriffsrechte des für das Gateway verwendeten Access Tokens', requiresAuth: true, )]
    #[ParameterDescription('firmaId', 'Die ID der Firma für die man die Schäden haben möchte', true, 1)]
    #[ParameterDescription('startOffset', 'Gibt basierend vom Limit den Anfangspunkt an.')]
    #[ParameterDescription('updatedAfter', 'Nur Schäden die nach diesem Datum geupdated wurden')]
    #[ParameterDescription('amountPages', 'Anzahl der Seiten die maximal geladen werden sollen. Werden mehr angefragt als verfügbar, so werden nur die verfügbaren geladen.')]
    #[ParameterDescription('includeDeleted', 'Includes Entities, that would normally not be included, because they are deleted')]
    public function fetchForFirma(
        int $firmaId,
        ?Carbon $updatedAfter = null,
        int $limit = 100,
        int $startOffset = 0,
        int $amountPages = 1,
        bool $includeDeleted = false,
    ): LazyCollection {
        $params = [
            'updatedAfter' => $updatedAfter?->toDateTimeString(),
            'includeDeleted' => $includeDeleted,
            'limit' => $limit,
            'offset' => $startOffset,
        ];

        return PaginableRequestCollectionBuilder::build(
            $this->getUrl($firmaId),
            $params,
            $this->gateway,
            $this->getPageParamsCallable(),
            static fn (array $rawSchaden): Schaden => SchadenFactory::convert($rawSchaden),
            $amountPages,
        );
    }

    /**
     * @return LazyCollection<int, Schaden>
     */
    #[Description(description: 'Gibt alle Schäden zu einer Firma zurück. Eingeschränkt durch die Zugriffsrechte des für das Gateway verwendeten Access Tokens', requiresAuth: true, )]
    #[ParameterDescription('firmaId', 'Die ID der Firma für die man die Schäden haben möchte', true, 1)]
    #[ParameterDescription('limit', 'Limitiert die Anzahl der Schäden, die pro Seite zurückgegeben werden')]
    #[ParameterDescription('startOffset', 'Gibt basierend vom Limit den Anfangspunkt an.')]
    #[ParameterDescription('updatedAfter', 'Nur Schäden die nach diesem datum geupdated wurden')]
    #[ParameterDescription('includeDeleted', 'Includes Entities, that would normaly not be included, because they are deleted')]
    public function fetchAllForFirma(
        int $firmaId,
        ?Carbon $updatedAfter = null,
        int $startOffset = 0,
        int $limit = 100,
        bool $includeDeleted = false,
    ): LazyCollection {
        return $this->fetchForFirma(
            firmaId: $firmaId,
            updatedAfter: $updatedAfter,
            limit: $limit,
            startOffset: $startOffset,
            amountPages: PHP_INT_MAX,
            includeDeleted: $includeDeleted,
        );
    }

    /**
     * @return LazyCollection<int, Schaden>
     */
    #[Description(description: 'Gibt eine bestimmte Anzahl der Schäden zu einer Firma zurück. Eingeschränkt durch die Zugriffsrechte des für das Gateway verwendeten Access Tokens', requiresAuth: true, )]
    #[ParameterDescription('startOffset', 'Gibt basierend vom Limit den Anfangspunkt an.')]
    #[ParameterDescription('updatedAfter', 'Nur Schäden die nach diesem Datum geupdated wurden')]
    #[ParameterDescription('amountPages', 'Anzahl der Seiten die maximal geladen werden sollen. Werden mehr angefragt als verfügbar, so werden nur die verfügbaren geladen.')]
    #[ParameterDescription('includeDeleted', 'Includes Entities, that would normally not be included, because they are deleted')]
    public function fetchAll(
        ?Carbon $updatedAfter = null,
        int $limit = 100,
        int $startOffset = 0,
        int $amountPages = 1,
        bool $includeDeleted = false,
    ): LazyCollection {
        $params = [
            'updatedAfter' => $updatedAfter?->toDateTimeString(),
            'includeDeleted' => $includeDeleted,
            'limit' => $limit,
            'offset' => $startOffset,
        ];

        return PaginableRequestCollectionBuilder::build(
            self::URL_INDEX,
            $params,
            $this->gateway,
            $this->getPageParamsCallable(),
            static fn (array $rawSchaden): Schaden => SchadenFactory::convert($rawSchaden),
            $amountPages,
        );
    }

    /**
     * @param int $id
     *
     * @return Schaden
     */
    #[Description(description: 'Gibt einen Schaden zurück. Eingeschränkt durch die Zugriffsrechte des für das Gateway verwendeten Access Tokens', requiresAuth: true, )]
    #[ParameterDescription('id', 'Die ID des Schadens den man haben möchte', true, 1)]
    public function get(
        int $id,
    ): Schaden {
        return SchadenFactory::convert(
            $this->getData(
                sprintf(
                    self::URL_SINGLE_INDEX,
                    $id,
                ),
            ),
        );
    }

    private function getUrl(int $firmaId): string
    {
        return sprintf(self::URL_FIRMA_INDEX, $firmaId);
    }

    private function getPageParamsCallable(): Closure
    {
        return static function (array $params, ?array $lastElementinResponse) {
            $params['updatedAfter'] = $lastElementinResponse['updatedAt'] ?? $params['updatedAfter'];
            $params['updatedAfterId'] = $lastElementinResponse['vertragId'] ?? $params['updatedAfterId'] ?? null;

            return $params;
        };
    }
}
