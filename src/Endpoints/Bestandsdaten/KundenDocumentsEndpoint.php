<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Demv\ProfessionalworksSdk\Endpoints\AbstractEndpoint;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Document;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Factory\DocumentFactory;
use Demv\ProfessionalworksSdk\PaginableRequestCollectionBuilder;
use Demv\SdkFramework\Attributes\Description;
use Demv\SdkFramework\Attributes\EndpointDescription;
use Demv\SdkFramework\Attributes\ParameterDescription;
use Illuminate\Support\LazyCollection;

#[EndpointDescription(
    description: 'Zugriff auf alle Dokumente eines Kunden, so wie auf spezifische Dokumente.',
    longDescription: 'In PW heißen diese Dokumente ClientFiles.',
)]
class KundenDocumentsEndpoint extends AbstractEndpoint
{
    private const URL_KUNDENAKTE = 'home/kundenakte/%u';
    private const URL_KUNDENAKTE_DOKUMENTE_INDEX = self::URL_KUNDENAKTE . '/dokumente';
    private const URL_KUNDENAKTE_DOKUMENTE_GET = self::URL_KUNDENAKTE . '/dokument/%u/meta';
    private const URL_KUNDENAKTE_DOKUMENTE_PRESIGNED_URL = 'kundenakte/api/dokument/getPresignedUrl';

    #[Description(
        description: 'Informationen über ein Dokument eines Kunden. Enthält auch die Url um die Datei herunterzuladen.',
    )]
    #[ParameterDescription('kundeId', 'Die ID des Kunden', true, 1)]
    #[ParameterDescription('documentId', 'Die ID des Dokuments', true, 1)]
    public function get(int $kundeId, int $documentId): Document
    {
        $raw = $this->getData(sprintf(self::URL_KUNDENAKTE_DOKUMENTE_GET, $kundeId, $documentId));

        return DocumentFactory::toDocument($raw);
    }

    #[Description(description: 'Kurzlebige presigned S3 Url für das Dokument', )]
    #[ParameterDescription('clientId', 'Die ID des Kunden', true, 1)]
    #[ParameterDescription('documentId', 'Die ID des Dokuments', true, 1)]
    public function getPresignedUrl(int $clientId, int $documentId): string
    {
        $data = $this->getData(
            self::URL_KUNDENAKTE_DOKUMENTE_PRESIGNED_URL,
            [
                'clientId' => $clientId,
                'documentId' => $documentId,
            ],
        );

        return $data['url'];
    }

    /**
     * @return LazyCollection<int, Document>
     */
    #[Description(
        description: 'Informationen über alle Dokumente eines Kunden. Enthält nicht die Url der Dokumente.',
    )]
    #[ParameterDescription('kundeId', 'Die ID des Kunden', true, 1)]
    #[ParameterDescription('filter', 'Ein Filterobjekt um die Anzahl der Dokumente einzugrenzen')]
    #[ParameterDescription('limit', 'Anzahl der Dokumente, die pro Seite zurückgegeben werden')]
    #[ParameterDescription('startOffset', 'Beginn der Collection')]
    public function fetchForKunde(
        int $kundeId,
        KundenDocumentsFilter $filter = null,
        int $limit = 100,
        int $startOffset = 0,
    ): LazyCollection {
        $params = [];

        if ($filter?->getVertrag() !== null) {
            $params['contract'] = $filter->getVertrag();
        }
        if ($filter?->getGesellschaft() !== null) {
            $params['company'] = $filter->getGesellschaft();
        }
        if ($filter?->getDocumentType() !== null) {
            $params['type'] = $filter->getDocumentType();
        }
        if ($filter?->getQuery() !== null) {
            $params['query'] = $filter->getQuery();
        }

        $params['limit'] = $limit;
        $params['offset'] = $startOffset;

        return PaginableRequestCollectionBuilder::build(
            sprintf(self::URL_KUNDENAKTE_DOKUMENTE_INDEX, $kundeId),
            $params,
            $this->gateway,
            static function ($params) use ($limit) {
                $params['offset'] += $limit;

                return $params;
            },
            static fn (array $raw): Document => DocumentFactory::toDocument($raw),
        );
    }
}
