<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

final class KundenDocumentsFilter
{
    private const NONE = 'none';

    private ?string $gesellschaft = null;
    private ?string $vertrag = null;
    private ?int $documentType = null;
    private string $query = '';

    public function withoutGesellschaft(): self
    {
        $this->gesellschaft = self::NONE;

        return $this;
    }

    public function withoutVertrag(): self
    {
        $this->vertrag = self::NONE;

        return $this;
    }

    public function withGesellschaft(int $gesellschaftId): self
    {
        $this->gesellschaft = (string) $gesellschaftId;

        return $this;
    }

    public function withVertrag(int $vertragId): self
    {
        $this->vertrag = (string) $vertragId;

        return $this;
    }

    public function withDocumentType(int $documentTypeId): self
    {
        $this->documentType = $documentTypeId;

        return $this;
    }

    public function searchFor(string $query): self
    {
        $this->query = $query;

        return $this;
    }

    public function getGesellschaft(): ?string
    {
        return $this->gesellschaft;
    }

    public function getVertrag(): ?string
    {
        return $this->vertrag;
    }

    public function getDocumentType(): ?int
    {
        return $this->documentType;
    }

    public function getQuery(): ?string
    {
        return strlen($this->query) > 0 ? $this->query : null;
    }
}
