<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\VertragStatus;

class VertragsStatusFactory
{
    public static function toVertragsStatus(array $statusArray): VertragStatus
    {
        return new VertragStatus(
            id: (int) $statusArray['id'],
            name: (string) $statusArray['name'],
        );
    }
}
