<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Factory;

use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\CountryEndpoint;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Adresse;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\KundeDetails;
use Demv\SdkFramework\Gateway\GatewayInterface;

class KundeDetailsFactory
{
    public static function toKundeDetails(array $kundeDetailsArray, GatewayInterface $gateway): KundeDetails
    {
        $countryEndpoint = new CountryEndpoint($gateway);

        $adressen = array_map(static function ($set) use ($countryEndpoint): Adresse {
            if (($set['landIso'] ?? '') !== '') {
                $country = $countryEndpoint->getCountryByIso($set['landIso']);
            } else {
                $country = null;
            }

            return new Adresse(
                strasse: $set['strasse'] ?? null,
                hausnummer: $set['hausnummer'] ?? null,
                adresszusatz: $set['adresszusatz'] ?? null,
                plz: $set['plz'] ?? null,
                ort: $set['ort'] ?? null,
                land_ISO: $set['landIso'] ?? null,
                country: $country,
                isHauptAdresse: $set['hauptAdresse'] ?? null,
            );
        }, $kundeDetailsArray['adressen']['adressen'] ?? []);

        return new KundeDetails(
            id: (int) $kundeDetailsArray['meta']['id'],
            firstName: $kundeDetailsArray['grunddaten']['vorname'] ?? null,
            lastName: $kundeDetailsArray['grunddaten']['nachname'],
            userId: (int) $kundeDetailsArray['meta']['id'],
            salutationId: (int) $kundeDetailsArray['grunddaten']['anrede'],
            email: $kundeDetailsArray['kontakt']['email'] ?? null,
            addresses: $adressen,
            title: $kundeDetailsArray['grunddaten']['titel'] ?? null,
        );
    }
}
