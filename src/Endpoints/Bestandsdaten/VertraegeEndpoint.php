<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Carbon\Carbon;
use Closure;
use Demv\ProfessionalworksSdk\Endpoints\AbstractEndpoint;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Vertrag;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\VertragStatus;
use Demv\ProfessionalworksSdk\PaginableRequestCollectionBuilder;
use Demv\SdkFramework\Attributes\Description;
use Demv\SdkFramework\Attributes\EndpointDescription;
use Demv\SdkFramework\Attributes\ParameterDescription;
use Demv\SdkFramework\Collection\Builder\SinglePageCollectionBuilder;
use Illuminate\Support\LazyCollection;

#[EndpointDescription(
    description: 'Vertrag-Bestandsdaten - Ein reduziertes Datenset der Verträge.',
    longDescription: '
    //TODO: ACHTUNG: Es kann vorkommen, dass hier Schäden ausgespielt werden: Siehe https://demvsystems.atlassian.net/browse/VORGAENGE-826
    Die Daten dienen der Synchronisierung der Basisdaten eines Vertrags. ' .
    'Genutzt werden diese Daten beispielsweise in den Vorgängen um Vorgänge mit Verträgen verknüpfen zu können',
)]
class VertraegeEndpoint extends AbstractEndpoint
{
    private const URL_FIRMA_INDEX = '/api/bestandsdaten/firma/%s/vertraege';
    private const URL_INDEX = '/api/bestandsdaten/vertraege';
    private const URL_SINGLE_INDEX = '/api/bestandsdaten/vertraege/%s';
    private const URL_VERTRAG_STATUS = '/stammdaten/api/vertragsstatus/all';

    /**
     * @return LazyCollection<int, Vertrag>
     */
    #[Description(description: 'Gibt eine bestimmte Anzahl der Verträge zu einer Firma zurück. Eingeschränkt durch die Zugriffsrechte des für das Gateway verwendeten Access Tokens', requiresAuth: true, )]
    #[ParameterDescription('firmaId', 'Die ID der Firma für die man die Verträge haben möchte', true, 1)]
    #[ParameterDescription('startOffset', 'Gibt basierend vom Limit den Anfangspunkt an.')]
    #[ParameterDescription('updatedAfter', 'Nur Verträge die nach diesem datum geupdated wurden')]
    #[ParameterDescription('amountPages', 'Anzahl der Seiten die maximal geladen werden sollen. Werden mehr angefragt als verfügbar, so werden nur die verfügbaren geladen.')]
    #[ParameterDescription('includeDeleted', 'Includes Entities, that would normally not be included, because they are deleted')]
    public function fetchForFirma(
        int $firmaId,
        ?Carbon $updatedAfter = null,
        int $limit = 100,
        int $startOffset = 0,
        int $amountPages = 1,
        bool $includeDeleted = false,
    ): LazyCollection {
        $url = $this->getUrl($firmaId);
        $params = [
            'updatedAfter' => $updatedAfter?->toDateTimeString(),
            'includeDeleted' => $includeDeleted,
            'limit' => $limit,
            'offset' => $startOffset,
        ];

        return PaginableRequestCollectionBuilder::build(
            $url,
            $params,
            $this->gateway,
            $this->getPageParamsCallable(),
            static fn (array $rawVertrag): Vertrag => VertragFactory::convert($rawVertrag),
            $amountPages,
        );
    }

    /**
     * @return LazyCollection<int, Vertrag>
     */
    #[Description(description: 'Gibt alle Verträge zu einer Firma zurück. Eingeschränkt durch die Zugriffsrechte des für das Gateway verwendeten Access Tokens', requiresAuth: true, )]
    #[ParameterDescription('firmaId', 'Die ID der Firma für die man die Verträge haben möchte', true, 1)]
    #[ParameterDescription('limit', 'Limitiert die Anzahl der Verträge, die pro Seite zurückgegeben werden')]
    #[ParameterDescription('startOffset', 'Gibt basierend vom Limit den Anfangspunkt an.')]
    #[ParameterDescription('updatedAfter', 'Nur Verträge die nach diesem datum geupdated wurden')]
    #[ParameterDescription('includeDeleted', 'Includes Entities, that would normaly not be included, because they are deleted')]
    public function fetchAllForFirma(
        int $firmaId,
        ?Carbon $updatedAfter = null,
        int $startOffset = 0,
        int $limit = 100,
        bool $includeDeleted = false,
    ): LazyCollection {
        return $this->fetchForFirma(
            firmaId: $firmaId,
            updatedAfter: $updatedAfter,
            limit: $limit,
            startOffset: $startOffset,
            amountPages: PHP_INT_MAX,
            includeDeleted: $includeDeleted,
        );
    }

    /**
     * @return LazyCollection<int, Vertrag>
     */
    #[Description(description: 'Gibt eine bestimmte Anzahl der Verträge zu einer Firma zurück. Eingeschränkt durch die Zugriffsrechte des für das Gateway verwendeten Access Tokens', requiresAuth: true, )]
    #[ParameterDescription('startOffset', 'Gibt basierend vom Limit den Anfangspunkt an.')]
    #[ParameterDescription('updatedAfter', 'Nur Verträge die nach diesem datum geupdated wurden')]
    #[ParameterDescription('amountPages', 'Anzahl der Seiten die maximal geladen werden sollen. Werden mehr angefragt als verfügbar, so werden nur die verfügbaren geladen.')]
    #[ParameterDescription('includeDeleted', 'Includes Entities, that would normally not be included, because they are deleted')]
    public function fetchAll(
        ?Carbon $updatedAfter = null,
        int $limit = 2000,
        int $startOffset = 0,
        int $amountPages = 1,
        bool $includeDeleted = false,
    ): LazyCollection {
        $params = [
            'updatedAfter' => $updatedAfter?->toDateTimeString(),
            'includeDeleted' => $includeDeleted,
            'limit' => $limit,
            'offset' => $startOffset,
        ];

        return PaginableRequestCollectionBuilder::build(
            self::URL_INDEX,
            $params,
            $this->gateway,
            $this->getPageParamsCallable(),
            static fn (array $rawVertrag): Vertrag => VertragFactory::convert($rawVertrag),
            $amountPages,
        );
    }

    /**
     * @param int $id
     *
     * @return Vertrag
     */
    #[Description(description: 'Gibt einen Vertrag zurück. Eingeschränkt durch die Zugriffsrechte des für das Gateway verwendeten Access Tokens', requiresAuth: true, )]
    #[ParameterDescription('id', 'Die ID des Vertrages den man haben möchte', true, 1)]
    public function get(
        int $id,
    ): Vertrag {
        return VertragFactory::convert(
            $this->getData(
                sprintf(
                    self::URL_SINGLE_INDEX,
                    $id,
                ),
            ),
        );
    }

    /**
     * @return LazyCollection<int, VertragStatus>
     */
    #[Description(description: 'Gibt alle Status zurück, die ein Vertrag annehmen kann')]
    public function allStatus(): LazyCollection
    {
        $getStatus = $this->createGetItemsByUrlFn(self::URL_VERTRAG_STATUS, includeKey: true);

        return SinglePageCollectionBuilder::build(
            $getStatus,
            static function (array $statusResponse) {
                $key = $statusResponse['key'];
                $value = $statusResponse['value'];

                return $key === 'data' ? $value : null;
            },
        )
            ->filter()
            ->flatten(1)
            ->map(static fn (array $item) => VertragsStatusFactory::toVertragsStatus($item));
    }

    private function getUrl(int $firmaId): string
    {
        return sprintf(self::URL_FIRMA_INDEX, $firmaId);
    }

    private function getPageParamsCallable(): Closure
    {
        return static function (array $params, ?array $lastElementinResponse) {
            $params['updatedAfter'] = $lastElementinResponse['updatedAt'] ?? $params['updatedAfter'];
            $params['updatedAfterId'] = $lastElementinResponse['vertragId'] ?? $params['updatedAfterId'] ?? null;

            return $params;
        };
    }
}
