<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Demv\ProfessionalworksSdk\Endpoints\AbstractEndpoint;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\KundeDetails;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\KundeSalutation;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Factory\KundeDetailsFactory;
use Demv\SdkFramework\Attributes\Description;
use Demv\SdkFramework\Attributes\EndpointDescription;
use Demv\SdkFramework\Attributes\ParameterDescription;

#[EndpointDescription(
    description: 'Detailliertere Kunden-Bestandsdaten',
)]
class KundenDetailsEndpoint extends AbstractEndpoint
{
    private const URL_KUNDENAKTE = 'home/kundenakte/%u/kunde';
    private const URL_SALUTATION = '/api/stammdaten/client/%u/salutation';

    #[Description(
        description: 'Ein Detailliertes Set an Infos über einen Kunden',
    )]
    #[ParameterDescription('kundeId', 'Die ID des Kunden', true, 1)]
    public function getDetails(int $kundeId): KundeDetails
    {
        $data = $this->getData(sprintf(self::URL_KUNDENAKTE, $kundeId));

        return KundeDetailsFactory::toKundeDetails($data['client'] ?? [], $this->gateway);
    }

    #[Description(
        description: 'Mögliche Anreden eines Kunden',
    )]
    #[ParameterDescription('kundeId', 'Die ID des Kunden', true, 1)]
    public function getSalutation(int $kundeId): KundeSalutation
    {
        $data = $this->getData(sprintf(self::URL_SALUTATION, $kundeId));

        return new KundeSalutation(
            salutation: $data['salutation'] ?? null,
            salutationVariation: $data['salutation_variation'] ?? null,
            salutationType: $data['salutation_type'] ?? null,
        );
    }
}
