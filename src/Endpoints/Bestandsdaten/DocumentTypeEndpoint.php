<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Demv\ProfessionalworksSdk\Endpoints\AbstractEndpoint;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\DocumentType;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Factory\DocumentTypeFactory;
use Demv\SdkFramework\Attributes\Description;
use Demv\SdkFramework\Attributes\EndpointDescription;
use Illuminate\Support\Collection;

#[EndpointDescription(
    description: 'Zugriff auf eine Liste aller Dokumentkategorien.',
)]
class DocumentTypeEndpoint extends AbstractEndpoint
{
    private const URL_INDEX = 'kundenakte/api/documentType/index';

    /**
     * @return Collection<int, DocumentType>
     */
    #[Description(description: 'Alle verfügbaren Dokumenttypen (Id, Name und Anwendbarkeit)')]
    public function all(): Collection
    {
        $rawArr = $this->getData(static::URL_INDEX);

        return Collection::make($rawArr)
            ->map(static fn (array $rawType) => DocumentTypeFactory::toDocumentType($rawType));
    }
}
