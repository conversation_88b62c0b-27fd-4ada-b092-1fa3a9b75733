<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Carbon\Carbon;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Schaden;

class SchadenFactory
{
    public static function convert(array $datenArray): Schaden
    {
        return new Schaden(
            id: (int) $datenArray['vertragId'],
            vertragsnummer: $datenArray['vertragsnummer'] ?? '',
            sparteId: (int) $datenArray['sparteId'],
            userId: (int) $datenArray['userId'],
            vertriebswegId: (int) $datenArray['vertriebswegId'],
            kundeId: (int) $datenArray['kundeId'],
            updatedAt: new Carbon($datenArray['updatedAt']),
            status: $datenArray['status'] !== null ? (int) $datenArray['status'] : null,
            agencyId: $datenArray['agencyId'] !== null ? (int) $datenArray['agencyId'] : null,
            kfzKennzeichen: $datenArray['kfzKennzeichen'] !== '' ? $datenArray['kfzKennzeichen'] : null,
            isDeleted: isset($datenArray['isDeleted']) ? (bool) $datenArray['isDeleted'] : null,
            schadenNummer: $datenArray['schadenNummer'] ?? null,
            schadenDatum: isset($datenArray['schadenDatum']) ? new Carbon($datenArray['schadenDatum']) : null,
            schadenAbgeschlossen: isset($datenArray['schadenAbgeschlossen']) ? (bool) $datenArray['schadenAbgeschlossen'] : null,
            parentContractId: (isset($datenArray['parentContractId']) && (int) $datenArray['parentContractId'] > 0) ? (int) $datenArray['parentContractId'] : null,
            gesellschaftId: $datenArray['gesellschaftId'] !== null ? (int) $datenArray['gesellschaftId'] : null,
        );
    }
}
