<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten;

use Demv\ProfessionalworksSdk\Endpoints\AbstractEndpoint;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Dtos\Country;
use Demv\ProfessionalworksSdk\Endpoints\Bestandsdaten\Factory\CountryFactory;
use Demv\SdkFramework\Attributes\ParameterDescription;

class CountryEndpoint extends AbstractEndpoint
{
    private const URL_COUNTRY_ISO = 'stammdaten/api/country/countrybyiso/iso/%s';
    private const URL_COUNTRY_ID = 'stammdaten/api/country/countrybyid/id/%u';

    #[ParameterDescription(name: 'isoCode', description: 'Den ISO Code, den ich habe', hasExampleValue: true, exampleValue: 'DE')]
    public function getCountryByIso(string $isoCode): Country
    {
        $url = sprintf(self::URL_COUNTRY_ISO, $isoCode);
        $data = $this->getData($url);

        return CountryFactory::toCountry($data[0]);
    }

    #[ParameterDescription(name: 'id', description: 'Die ID die ich suche', hasExampleValue: true, exampleValue: 1)]
    public function getCountryById(int $id): Country
    {
        $url = sprintf(self::URL_COUNTRY_ID, $id);
        $data = $this->getData($url);

        return CountryFactory::toCountry($data[0]);
    }
}
