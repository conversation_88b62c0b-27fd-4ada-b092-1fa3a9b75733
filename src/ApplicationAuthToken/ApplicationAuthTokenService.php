<?php

declare(strict_types=1);

namespace Demv\ProfessionalworksSdk\ApplicationAuthToken;

use Demv\ProfessionalworksSdk\Exceptions\ApplicationTokenException;
use Demv\ProfessionalworksSdk\Exceptions\ProfessionalWorksSdkException;
use Demv\SdkFramework\Gateway\ExpectJsendResponseMiddleware;
use Demv\SdkFramework\Gateway\GatewayInterface;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use InvalidArgumentException;
use UnexpectedValueException;

class ApplicationAuthTokenService
{
    protected const URL_AUTH_APPLICATION_TOKEN = '/auth/auth/applicationToken';

    public function __construct(
        private string $professionalWorksPublicKey,
        protected GatewayInterface $gateway,
    ) {
    }

    /**
     * @throws ApplicationTokenException
     * @throws Exception
     */
    public function validateToken(string $token): object
    {
        $decoded = $this->decodeToken($token);

        if (!property_exists($decoded, 'exp')) {
            throw new ApplicationTokenException(
                'Der Token enthält nicht den erwarteten Payload',
                401,
            );
        }

        return $decoded;
    }

    /**
     * @throws ApplicationTokenException
     */
    public function refreshToken(): object
    {
        return $this->getTokenFromPw();
    }

    /**
     * @throws ApplicationTokenException
     */
    public function getTokenFromPw(): object
    {
        $token = $this->getUnvalidatedToken();

        return $this->validateToken($token);
    }

    /**
     * @throws ApplicationTokenException
     */
    public function getRawTokenFromPw(): string
    {
        $token = $this->getUnvalidatedToken();

        $this->validateToken($token);

        return $token;
    }

    /**
     * @throws ApplicationTokenException
     */
    private function getUnvalidatedToken(): string
    {
        try {
            $response = $this->gateway->get(
                uri: self::URL_AUTH_APPLICATION_TOKEN,
                middlewares: [new ExpectJsendResponseMiddleware()],
                throw: true,
            );

            $contents = json_decode($response->getBody()->getContents(), true);

            return $contents['data']['token'];
        } catch (ProfessionalWorksSdkException $sdkException) {
            throw new ApplicationTokenException(message: 'Could not get a valid Token', previous: $sdkException);
        }
    }

    /**
     * @throws Exception
     */
    private function decodeToken(string $token): object
    {
        try {
            return JWT::decode($token, new Key($this->professionalWorksPublicKey, 'RS512'));
        } catch (
        InvalidArgumentException|
        UnexpectedValueException $exception
        ) {
            throw new ApplicationTokenException(
                $exception->getMessage(),
                401,
                $exception,
            );
        }
    }
}
