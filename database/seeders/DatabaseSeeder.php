<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class DatabaseSeeder extends Seeder
{
    /**
     * Run the database seeds via Sync from PW.
     */
    public function run(): void
    {
        Artisan::call('down');
        Artisan::call('vorgaenge:sync:initial', [], $this->command->getOutput());
        Artisan::call('up');
    }
}
