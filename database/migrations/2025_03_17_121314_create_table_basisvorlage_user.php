<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const TABLE = 'basisvorlage_user';
    private const USER_COL = 'user_id';
    private const VORGANGSTYP_COL = 'vorgangstyp_id';

    public function up(): void
    {
        Schema::create(self::TABLE, function (Blueprint $table) {
            $table->id();
            $table->foreignId(self::USER_COL)->constrained()->onDelete('cascade');
            $table->foreignId(self::VORGANGSTYP_COL)
                ->constrained()
                ->references('id')
                ->on('vorgang_typen')
                ->onDelete('cascade');
            $table->morphs('vorlage');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists(self::TABLE);
    }
};
