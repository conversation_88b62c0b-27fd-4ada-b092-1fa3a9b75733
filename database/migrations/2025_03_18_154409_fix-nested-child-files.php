<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Set the parent_id of all files to the parent_id of their parent
        DB::table('files')
            ->join('files as parent', function ($join) {
                $join->on('files.parent_id', '=', 'parent.id')
                    ->where('parent.parent_id', '<>', null);
            })
            ->update(['files.parent_id' => DB::raw('parent.parent_id')]);
    }
};
