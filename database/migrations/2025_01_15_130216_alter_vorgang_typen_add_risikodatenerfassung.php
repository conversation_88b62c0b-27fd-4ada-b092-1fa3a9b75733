<?php

declare(strict_types=1);

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    private const VORGANGSTYP_TITEL = 'Risikodatenerfassung';

    public function up(): void
    {
        DB::table('vorgang_typen')->insert([
            'titel' => self::VORGANGSTYP_TITEL,
            'external_support_assignment_subject_id' => null,
            'is_email' => true,
            'is_brief' => false,
            'empfaenger_typ' => 'kunde',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function down(): void
    {
        DB::table('vorgang_typen')
            ->where('titel', '=', self::VORGANGSTYP_TITEL)
            ->delete();
    }
};
