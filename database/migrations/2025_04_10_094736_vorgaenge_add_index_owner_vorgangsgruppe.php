<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('vorgaenge', function (Blueprint $table) {
            $table->index(['owner_id', 'vorgangs_gruppe']);
        });
    }

    public function down(): void
    {
        Schema::table('vorgaenge', function (Blueprint $table) {
            $table->dropIndex(['owner_id', 'vorgangs_gruppe']);
        });
    }
};
