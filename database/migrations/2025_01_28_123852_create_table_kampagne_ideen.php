<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kampagne_ideen', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('kampagne_titel');
            $table->foreignId('mail_vorlage_id')->nullable()->references('id')->on('vorlagen');
            $table->foreignId('brief_vorlage_id')->nullable()->references('id')->on('vorlagen');
            $table->json('kundensuche_vorlage')->nullable();
            $table->boolean('is_highlighted')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kampagne_ideen');
    }
};
