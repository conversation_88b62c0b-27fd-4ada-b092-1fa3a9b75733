<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        DB::table('vorgang_typen')->insertGetId([
            'titel' => 'Europace - Freischaltung Professional works',
            'external_support_assignment_subject_id' => null,
            'is_email' => true,
            'is_brief' => false,
            'empfaenger_typ' => 'gesellschaft',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    public function down(): void
    {
        DB::table('vorgang_typen')
            ->where('titel', 'Europace - Freischaltung Professional works')
            ->delete();
    }
};
