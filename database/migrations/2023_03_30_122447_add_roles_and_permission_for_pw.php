<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    const  ROLE_NAMES = [
        'admin' => 'Admin',
        'kunde' => 'Kunde',
        'testnutzer' => 'Testnutzer',
        'gesellschaft' => 'Gesellschaft',
        'gesellschaft_ansprechpartner' => 'Gesellschaft - Ansprechpartner',
        'hauptvermittler' => 'Hauptvermittler',
        'innendienst' => 'Innendienst',
        'vermittler' => 'Vermittler',
        'white_label' => 'White Label',
    ];

    const PERMISSION_NAMES = [
        'email_versenden' => 'emails versenden',
        'vorgaenge_verwalten' => 'vorgaenge verwenden',
        'benutzer_firma_anzeigen' => 'firmenmitglieder anzeigen',
        'superadmin' => 'superadmin',
        'admin' => 'admin',
    ];

    /**
     * Run the migrations.
     */
    public function up()
    {
        // Create Roles
        Role::create([
            'name' => self::ROLE_NAMES['vermittler'],
        ]);
        Role::create([
            'name' => self::ROLE_NAMES['gesellschaft'],
        ]);
        Role::create([
            'name' => self::ROLE_NAMES['white_label'],
        ]);
        $admin = Role::create([
            'name' => self::ROLE_NAMES['admin'],
        ]);
        Role::create([
            'name' => self::ROLE_NAMES['hauptvermittler'],
        ]);
        Role::create([
            'name' => self::ROLE_NAMES['innendienst'],
        ]);
        Role::create([
            'name' => self::ROLE_NAMES['kunde'],
        ]);
        Role::create([
            'name' => self::ROLE_NAMES['gesellschaft_ansprechpartner'],
        ]);
        Role::create([
            'name' => self::ROLE_NAMES['testnutzer'],
        ]);

        // Create Permissions
        Permission::create([
            'name' => self::PERMISSION_NAMES['superadmin'],
        ]);

        $adminroles = [];
        $adminroles[] = Permission::create([
            'name' => self::PERMISSION_NAMES['admin'],
        ]);
        $adminroles[] = Permission::create([
            'name' => self::PERMISSION_NAMES['email_versenden'],
        ]);
        $adminroles[] = Permission::create([
            'name' => self::PERMISSION_NAMES['vorgaenge_verwalten'],
        ]);
        $adminroles[] = Permission::create([
            'name' => self::PERMISSION_NAMES['benutzer_firma_anzeigen'],
        ]);

        $admin->syncPermissions($adminroles);
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Role::query()->delete();
        Permission::query()->delete();
    }
};
