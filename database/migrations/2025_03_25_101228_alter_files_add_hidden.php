<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const TABLE = 'files';
    private const COLUMN = 'hidden';

    public function up(): void
    {
        Schema::table(self::TABLE, static function (Blueprint $table): void {
            $table->boolean(self::COLUMN)->default(false)->after('owner_id');
        });
    }
    public function down(): void
    {
        Schema::table(self::TABLE, static function (Blueprint $table): void {
            $table->dropColumn(self::COLUMN);
        });
    }
};
