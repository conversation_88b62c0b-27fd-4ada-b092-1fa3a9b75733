<?php

use Illuminate\Database\Migrations\Migration;
use <PERSON><PERSON>\Permission\Models\Permission;

return new class extends Migration
{
    const KOMPLETTE_FIRMENDATEN_VERWALTEN = 'komplette firmendaten verwalten';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Permission::create([
            'name' => self::KOMPLETTE_FIRMENDATEN_VERWALTEN,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Permission::findByName(self::KOMPLETTE_FIRMENDATEN_VERWALTEN)->delete();
        app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
    }
};
