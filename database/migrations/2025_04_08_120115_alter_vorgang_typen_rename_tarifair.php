<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    private const VORGANGSTYP_ID = 100;
    private const VORGANGSTYP_TITEL_NEW = 'Login zu Tarifair (Barmenia - Adcuri)';
    private const VORGANGSTYP_TITEL_OLD = 'Login zu tariffair (Barmenia - Adcuri)';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('vorgang_typen')
            ->where('id', self::VORGANGSTYP_ID)
            ->update(['titel' => self::VORGANGSTYP_TITEL_NEW]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('vorgang_typen')
            ->where('id', self::VORGANGSTYP_ID)
            ->update(['titel' => self::VORGANGSTYP_TITEL_OLD]);
    }
};
