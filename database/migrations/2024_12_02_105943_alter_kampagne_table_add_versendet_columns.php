<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const TABLE = 'kampagnen';

    public function up(): void
    {
        Schema::table(self::TABLE, function (Blueprint $table) {
            $table->dateTime('geplant_at')->after('status')->nullable();
            $table->dateTime('versendet_at')->after('geplant_at')->nullable();
        });

        DB::statement('
            UPDATE kampagnen
            SET versendet_at = updated_at
            WHERE status = "abgeschlossen"
            OR status = "in_bearbeitung"
            AND versendet_at IS NULL
        ');
    }

    public function down(): void
    {
        Schema::table(self::TABLE, function (Blueprint $table) {
            $table->dropColumn('geplant_at');
            $table->dropColumn('versendet_at');
        });
    }
};
