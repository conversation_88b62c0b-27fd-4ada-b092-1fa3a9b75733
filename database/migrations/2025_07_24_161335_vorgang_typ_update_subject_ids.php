<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    private const TABLE = 'vorgang_typen';
    private const COLUMN = 'external_support_assignment_subject_id';

    public function up(): void
    {
        // GDV Datenlieferung zu DEMV & Falsche GDV Daten
        // Subject GDV und BiPro
        DB::table(self::TABLE)
            ->whereIn('id', [30, 41])
            ->update([self::COLUMN => 20]);

        // Beginnverlegung & Schadenrenta
        // Subject Maklerservice/Innendienst
        DB::table(self::TABLE)
            ->whereIn('id', [36, 38])
            ->update([self::COLUMN => 1]);
    }

    public function down(): void
    {
        // Zurück zu Maklerbetreuer/in
        DB::table(self::TABLE)
            ->whereIn('id', [30, 41, 36, 38])
            ->update([self::COLUMN => 6]);
    }
};
