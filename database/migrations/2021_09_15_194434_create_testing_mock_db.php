<?php

declare(strict_types=1);

use App\Testing\Fixtures\AbstractMockedResponse;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * This Migration can not be squashed because it is needed in the testing environment
 */
return new class extends Migration
{
    public function up()
    {
        $databaseName = Schema::connection($this->getConnection())->getConnection()->getDatabaseName();

        if ($databaseName === 'cypress') {
            Schema::create('testing_mock_db', static function (Blueprint $table) {
                $table->id();
                $table->string('method')->default(AbstractMockedResponse::HTTP_GET);
                $table->text('url');
                $table->text('body');
                $table->timestamps();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('testing_mock_db');
    }
};
