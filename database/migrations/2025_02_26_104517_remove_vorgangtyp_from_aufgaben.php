<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const TABLE = 'vorgaenge';

    public function up(): void
    {
        DB::table(self::TABLE)
            ->where('vorgangsart', 'aufgabe')
            ->where('service_id', 'vorgaenge')
            ->update(['vorgang_typ_id' => null]);
    }
};
