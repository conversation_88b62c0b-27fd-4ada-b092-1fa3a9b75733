<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $kampagne_ideen = [
            [
                'name' => 'Bestandsübertragungen',
                'description' => 'Diese Kampagne richtet sich an alle Kunden ohne Maklervertrag und mit Fremdverträgen, um deren Verträge in Ihre Betreuung zu überführen.',
                'kampagne_titel' => 'Bestandsübertragungen',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'dokumenttyp' => '56',
                    'vertragstyp' => '2'
                ]),
                'is_highlighted' => false,
            ],
            [
                'name' => 'Kieferorthopädische Absicherung für Kinder',
                'description' => 'Eltern von Kindern zwischen 7 und 11 Jahren können mit einer Zahnzusatzversicherung die Kosten für mögliche kieferorthopädische Behandlungen absichern, da diese oft nicht von der gesetzlichen Krankenversicherung übernommen werden.',
                'kampagne_titel' => 'Kieferorthopädische Absicherung für Kinder',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'kinder' => true,
                    'kinderAlter' => [
                        'von' => 7,
                        'bis' => 11
                    ],
                    'vertragssparten' => [ '286' ],
                    'vertragsspartenAusschliessen' => true
                ]),
                'is_highlighted' => false,
            ],
            [
                'name' => 'Handwerker-Rentenversicherung',
                'description' => 'Informieren Sie selbstständige Handwerker über die Möglichkeit, sich nach 18 Jahren von der Deutschen Rentenversicherung zu befreien und privat für das Alter vorzusorgen.',
                'kampagne_titel' => 'Handwerker-Rentenversicherung',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'koerperlichTaetig' => [
                        'von' => 60,
                        'bis' => 100
                    ],
                    'kundenAlter' => [
                        'von' => 35,
                    ],
                    'zielgruppe' => [ '3' ],
                ]),
                'is_highlighted' => false,
            ],
            [
                'name' => 'BU-Alternativen für körperlich Tätige',
                'description' => 'Kampagne für körperlich tätige Kunden unter 50 Jahren, die keine Berufsunfähigkeitsversicherung haben – Information über alternative Arbeitskraftabsicherungen zum Schutz bei unerwartetem Ausfall.',
                'kampagne_titel' => 'BU-Alternativen für körperlich Tätige',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'koerperlichTaetig' => [
                        'von' => 60,
                        'bis' => 100
                    ],
                    'kundenAlter' => [
                        'bis' => 50,
                    ],
                    'vertragssparten' => [ '260' ],
                    'vertragsspartenAusschliessen' => true
                ]),
                'is_highlighted' => false,
            ],
            [
                'name' => 'Schulunfähigkeitsversicherung',
                'description' => 'Informieren Sie Eltern über die Vorteile einer Schulunfähigkeitsversicherung für ihre Kinder im Alter von 10-18 Jahren und warum diese Absicherung wichtig ist.',
                'kampagne_titel' => 'Schulunfähigkeitsversicherung',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'kinder' => true,
                    'kinderAlter' => [
                        'von' => 10,
                        'bis' => 18
                    ],
                ]),
                'is_highlighted' => false,
            ],
            [
                'name' => 'KV Zusatz bei Neugeborenen',
                'description' => 'Informieren Sie frischgebackene Eltern über die Vorteile einer Krankenhauszusatzversicherung und wie sie nach der Geburt die bestmögliche Versorgung für ihr Neugeborenes sichern können.',
                'kampagne_titel' => 'KV Zusatz bei Neugeborenen',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'kinder' => true,
                    'kinderAlter' => [
                        'von' => 0,
                        'bis' => 1
                    ],
                    'vertragssparten' => [ '285' ],
                    'vertragsspartenAusschliessen' => true
                ]),
                'is_highlighted' => false,
            ],
            [
                'name' => 'Check-Up im Finanzmanager',
                'description' => 'Kunden ohne Zugang zum Finanzmanager ansprechen und ihnen einen Check-Up-Link senden, um ihre Daten zu erweitern und damit besser beraten zu können.',
                'kampagne_titel' => 'Check-Up im Finanzmanager',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'finanzmanagerAccess' => false,
                ]),
                'is_highlighted' => true,
            ],
            [
                'name' => 'Weihnachtsgrüße',
                'description' => 'Versenden Sie herzliche Weihnachtsgrüße an Ihre Kunden und zeigen Sie Ihre Wertschätzung für das Vertrauen im vergangenen Jahr.',
                'kampagne_titel' => 'Weihnachtsgrüße',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => null,
                'is_highlighted' => false,
            ],
            [
                'name' => 'Ostergrüße',
                'description' => 'Senden Sie Ihren Kunden herzliche Osterwünsche und stärken Sie die Beziehung zu ihnen.',
                'kampagne_titel' => 'Ostergrüße',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => null,
                'is_highlighted' => true,
            ],
        ];

        DB::table('kampagne_ideen')->insert($kampagne_ideen);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('kampagne_ideen')->truncate();
    }
};
