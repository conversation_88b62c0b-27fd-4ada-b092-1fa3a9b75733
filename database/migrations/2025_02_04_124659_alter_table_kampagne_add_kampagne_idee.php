<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    private const TABLE = 'kampagnen';
    public function up(): void
    {
        Schema::table(self::TABLE, function (Blueprint $table) {
            $table->foreignId('kampagne_idee_id')->nullable()->after('empfaengers')->constrained('kampagne_ideen');
        });
    }

    public function down(): void
    {
        Schema::table(self::TABLE, function (Blueprint $table) {
            $table->dropForeign(['kampagne_idee_id']);
            $table->dropColumn('kampagne_idee_id');
        });
    }
};
