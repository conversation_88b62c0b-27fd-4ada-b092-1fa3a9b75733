<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    const TABLE_SPARTEN = 'sparten';
    const TABLE_VORGAENGE = 'vorgaenge';
    const TABLE_VERTRAEGE = 'vertraege';

    public function up(): void
    {
        Schema::table(self::TABLE_VORGAENGE, function (Blueprint $table) {
            $table->dropForeign(['sparte_id']);
        });

        DB::statement('UPDATE ' . self::TABLE_VORGAENGE . ' v
            JOIN ' . self::TABLE_SPARTEN . ' s ON v.sparte_id = s.id
            SET v.sparte_id = s.external_id');

        Schema::table(self::TABLE_SPARTEN, function (Blueprint $table) {
            $table->unsignedBigInteger('id')->change();
            $table->unsignedBigInteger('external_id')->change();
            $table->dropPrimary();
            $table->dropColumn('id');
        });

        Schema::table(self::TABLE_SPARTEN, function (Blueprint $table) {
            $table->renameColumn('external_id', 'id');
            $table->unique('id');
        });

        Schema::table(self::TABLE_VERTRAEGE, function (Blueprint $table) {
            $table->renameColumn('sparte_external_id', 'sparte_id');
        });

        Schema::table(self::TABLE_VORGAENGE, function (Blueprint $table) {
            $table->foreign('sparte_id')->references('id')->on(self::TABLE_SPARTEN);
        });
    }

    public function down(): void
    {
        Schema::table(self::TABLE_VORGAENGE, function (Blueprint $table) {
            $table->dropForeign(['sparte_id']);
        });

        Schema::table(self::TABLE_SPARTEN, function (Blueprint $table) {
            $table->dropUnique('sparten_id_unique');
            $table->renameColumn('id', 'external_id');
        });

        Schema::table(self::TABLE_SPARTEN, function (Blueprint $table) {
            $table->id()->first();
        });

        Schema::table(self::TABLE_VERTRAEGE, function (Blueprint $table) {
            $table->renameColumn('sparte_id', 'sparte_external_id');
        });

        DB::statement('UPDATE ' . self::TABLE_VORGAENGE . ' v
            JOIN ' . self::TABLE_SPARTEN . ' s ON v.sparte_id = s.external_id
            SET v.sparte_id = s.id');

        Schema::table(self::TABLE_VORGAENGE, function (Blueprint $table) {
            $table->foreign('sparte_id')->references('id')->on(self::TABLE_SPARTEN);
        });
    }
};
