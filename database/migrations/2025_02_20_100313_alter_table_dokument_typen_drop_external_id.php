<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    const TABLE_DOKUMENT_TYPEN = 'dokument_typen';
    const TABLE_FILES = 'files';

    public function up(): void
    {
        DB::statement('UPDATE ' . self::TABLE_FILES . ' f
            JOIN ' . self::TABLE_DOKUMENT_TYPEN . ' dt ON f.dokument_typ_id = dt.id
            SET f.dokument_typ_id = dt.external_id');

        Schema::table(self::TABLE_FILES, function (Blueprint $table) {
            $table->dropForeign(['dokument_typ_id']);
        });

        Schema::table(self::TABLE_DOKUMENT_TYPEN, function (Blueprint $table) {
            $table->unsignedBigInteger('id')->change();
            $table->unsignedBigInteger('external_id')->change();
            $table->dropPrimary();
            $table->dropColumn('id');
        });

        Schema::table(self::TABLE_DOKUMENT_TYPEN, function (Blueprint $table) {
            $table->renameColumn('external_id', 'id');
            $table->primary('id');
        });

        Schema::table(self::TABLE_FILES, function (Blueprint $table) {
            $table->foreign('dokument_typ_id')->references('id')->on(self::TABLE_DOKUMENT_TYPEN);
        });
    }

    public function down(): void
    {
        Schema::table(self::TABLE_FILES, function (Blueprint $table) {
            $table->dropForeign(['dokument_typ_id']);
        });

        Schema::table(self::TABLE_DOKUMENT_TYPEN, function (Blueprint $table) {
            $table->bigInteger('id')->change();
            $table->dropPrimary();
            $table->renameColumn('id', 'external_id');
        });

        Schema::table(self::TABLE_DOKUMENT_TYPEN, function (Blueprint $table) {
            $table->id()->first();
        });

        Schema::table(self::TABLE_FILES, function (Blueprint $table) {
            $table->foreign('dokument_typ_id')->references('id')->on(self::TABLE_DOKUMENT_TYPEN);
        });

        DB::statement('UPDATE ' . self::TABLE_FILES . ' f
            JOIN ' . self::TABLE_DOKUMENT_TYPEN . ' dt ON f.dokument_typ_id = dt.external_id
            SET f.dokument_typ_id = dt.id');
    }
};
