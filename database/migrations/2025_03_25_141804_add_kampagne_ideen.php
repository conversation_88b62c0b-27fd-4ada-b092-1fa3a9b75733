<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        $kampagneIdeen = [
            [
                'name' => 'Zahnersatzversicherung',
                'description' => 'Diese Kampagne richtet sich an alle Kunden ohne Zahnzusatzversicherung, Ziel ist der Neuabschluss der Zahnzusatzversicherung mit Zahnersatz.',
                'kampagne_titel' => 'Zahnersatzversicherung',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'kundenAlter' => [
                        'von' => 20,
                        'bis' => 60,
                    ],
                    'vertragssparten' => [
                        '286',
                    ],
                    'vertragsspartenAusschliessen' => true
                ]),
                'is_highlighted' => false,
            ],
            [
                'name' => 'Zahnbehandlung',
                'description' => 'Diese Kampagne richtet sich an alle Kunden ohne Zahnzusatzversicherung, die Wert auf eine professionelle Zahnreinigung und Zahnbehandlung legen.',
                'kampagne_titel' => 'Zahnbehandlung',
                'mail_vorlage_id' => null,
                'brief_vorlage_id' => null,
                'kundensuche_vorlage' => json_encode([
                    'kundenAlter' => [
                        'von' => 18,
                        'bis' => 55,
                    ],
                    'vertragssparten' => [
                        '286',
                    ],
                    'vertragsspartenAusschliessen' => true
                ]),
                'is_highlighted' => false,
            ]
        ];

        DB::table('kampagne_ideen')->insert($kampagneIdeen);
    }

    public function down(): void
    {
        DB::table('kampagne_ideen')->where('name', 'Zahnersatzversicherung')->delete();
        DB::table('kampagne_ideen')->where('name', 'Zahnbehandlung')->delete();
    }
};
