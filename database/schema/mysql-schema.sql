/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET NAMES utf8;
DROP TABLE IF EXISTS `bezuege`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `bezuege` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorgang_id` bigint unsigned NOT NULL,
  `benennung` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `referenz` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `bezuege_vorgang_id_foreign` (`vorgang_id`),
  KEY `bezuege_referenz_index` (`referenz`),
  CONSTRAINT `bezuege_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `bulk_changes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `bulk_changes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `action` enum('delete','complete_vorgang') COLLATE utf8mb4_unicode_ci NOT NULL,
  `model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ids` json NOT NULL,
  `status` enum('in_bearbeitung','abgeschlossen','fehler') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `bulk_changes_user_id_foreign` (`user_id`),
  CONSTRAINT `bulk_changes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `dokument_typen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dokument_typen` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_for_kunde` tinyint(1) NOT NULL,
  `is_for_user` tinyint(1) NOT NULL,
  `is_for_gesellschaft` tinyint(1) NOT NULL,
  `is_for_firma` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `email_metas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_metas` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `versendet_at` timestamp NULL DEFAULT NULL,
  `empfaenger` json NOT NULL,
  `absender` json NOT NULL,
  `cc` json DEFAULT NULL,
  `bcc` json DEFAULT NULL,
  `status` enum('versandbereit','in_versand','abgeschlossen','fehler') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'versandbereit',
  `status_text` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cc_actual` json DEFAULT NULL,
  `bcc_actual` json DEFAULT NULL,
  `empfaenger_actual` json DEFAULT NULL,
  `absender_actual` json DEFAULT NULL,
  `message_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mailer_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `erinnerung_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `erinnerung_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `versandart` enum('email','brief') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `betreff` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `versendet_at` timestamp NULL DEFAULT NULL,
  `empfaenger` json NOT NULL,
  `absender` json DEFAULT NULL,
  `cc` json DEFAULT NULL,
  `bcc` json DEFAULT NULL,
  `status` enum('entwurf','in_bearbeitung','versandbereit','in_versand','abgeschlossen','fehler') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cc_actual` json DEFAULT NULL,
  `bcc_actual` json DEFAULT NULL,
  `empfaenger_actual` json DEFAULT NULL,
  `absender_actual` json DEFAULT NULL,
  `brief_datum` date DEFAULT NULL,
  `brief` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `s3_content_ref` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `include_full_history` tinyint(1) NOT NULL DEFAULT '0',
  `email_meta_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `erinnerung_elemente_s3_content_ref_unique` (`s3_content_ref`),
  KEY `erinnerung_elemente_email_meta_id_foreign` (`email_meta_id`),
  CONSTRAINT `erinnerung_elemente_email_meta_id_foreign` FOREIGN KEY (`email_meta_id`) REFERENCES `email_metas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `externe_korrespondenz_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `externe_korrespondenz_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `uses_attachments` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `features`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `features` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `active_for_all` tinyint(1) NOT NULL DEFAULT '0',
  `active_for_user` text COLLATE utf8mb4_unicode_ci,
  `active_for_firma` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `files` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `mimetype` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `size` int DEFAULT NULL,
  `kundenakte_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `dokument_typ_id` bigint unsigned DEFAULT NULL,
  `status` enum('pending','uploaded','error') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `source` json DEFAULT NULL,
  `owner_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `files_owner_type_owner_id_index` (`owner_type`,`owner_id`),
  KEY `files_dokument_typ_id_foreign` (`dokument_typ_id`),
  KEY `files_parent_id_foreign` (`parent_id`),
  CONSTRAINT `files_dokument_typ_id_foreign` FOREIGN KEY (`dokument_typ_id`) REFERENCES `dokument_typen` (`id`),
  CONSTRAINT `files_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `files` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `firmen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `firmen` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `kuerzel` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `kuerzel_history` json DEFAULT NULL,
  `external_id` bigint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `external_updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `firmen_external_id_unique` (`external_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `folge_vorgang_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `folge_vorgang_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorgaenger_id` bigint unsigned NOT NULL,
  `nachfolger_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `folge_vorgang_elemente_vorgaenger_id_foreign` (`vorgaenger_id`),
  KEY `folge_vorgang_elemente_nachfolger_id_foreign` (`nachfolger_id`),
  CONSTRAINT `folge_vorgang_elemente_nachfolger_id_foreign` FOREIGN KEY (`nachfolger_id`) REFERENCES `vorgaenge` (`id`),
  CONSTRAINT `folge_vorgang_elemente_vorgaenger_id_foreign` FOREIGN KEY (`vorgaenger_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `gesellschaften`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gesellschaften` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abkuerzung` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_pool` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `gesellschaften_external_id_unique` (`external_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `gespraechsnotiz_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gespraechsnotiz_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('phone','on-site','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_from` datetime NOT NULL,
  `date_to` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `kampagne_messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `kampagne_messages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `kampagne_id` bigint unsigned NOT NULL,
  `vorgang_id` bigint unsigned DEFAULT NULL,
  `email_meta_id` bigint unsigned DEFAULT NULL,
  `kunde_external_id` bigint NOT NULL,
  `sender_external_id` bigint NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `adresse` json DEFAULT NULL,
  `informal` tinyint(1) NOT NULL DEFAULT '0',
  `tag_values` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `kampagne_messages_kampagne_id_foreign` (`kampagne_id`),
  KEY `kampagne_messages_kunde_external_id_foreign` (`kunde_external_id`),
  KEY `kampagne_messages_sender_external_id_foreign` (`sender_external_id`),
  KEY `kampagne_messages_vorgang_id_foreign` (`vorgang_id`),
  KEY `kampagne_messages_email_meta_id_foreign` (`email_meta_id`),
  CONSTRAINT `kampagne_messages_email_meta_id_foreign` FOREIGN KEY (`email_meta_id`) REFERENCES `email_metas` (`id`),
  CONSTRAINT `kampagne_messages_kampagne_id_foreign` FOREIGN KEY (`kampagne_id`) REFERENCES `kampagnen` (`id`),
  CONSTRAINT `kampagne_messages_kunde_external_id_foreign` FOREIGN KEY (`kunde_external_id`) REFERENCES `kunden` (`external_id`),
  CONSTRAINT `kampagne_messages_sender_external_id_foreign` FOREIGN KEY (`sender_external_id`) REFERENCES `users` (`external_id`),
  CONSTRAINT `kampagne_messages_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `kampagnen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `kampagnen` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `titel` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `versandart` enum('email','brief') COLLATE utf8mb4_unicode_ci NOT NULL,
  `vorgaenge_anlegen` tinyint(1) NOT NULL DEFAULT '0',
  `vorgang_id` bigint unsigned DEFAULT NULL,
  `im_namen_von` enum('mir','benutzer','vermittler') COLLATE utf8mb4_unicode_ci NOT NULL,
  `sender_external_id` bigint DEFAULT NULL,
  `ersteller_external_id` bigint NOT NULL,
  `empfaengers` json DEFAULT NULL,
  `formal_betreff` text COLLATE utf8mb4_unicode_ci,
  `formal_content` mediumtext COLLATE utf8mb4_unicode_ci,
  `informal_betreff` text COLLATE utf8mb4_unicode_ci,
  `informal_content` mediumtext COLLATE utf8mb4_unicode_ci,
  `attachments` json DEFAULT NULL,
  `status` enum('entwurf','in_bearbeitung','abgeschlossen','geplant') COLLATE utf8mb4_unicode_ci DEFAULT 'entwurf',
  `geplant_at` datetime DEFAULT NULL,
  `versendet_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `kampagnen_sender_external_id_foreign` (`sender_external_id`),
  KEY `kampagnen_ersteller_external_id_foreign` (`ersteller_external_id`),
  KEY `kampagnen_vorgang_id_foreign` (`vorgang_id`),
  CONSTRAINT `kampagnen_ersteller_external_id_foreign` FOREIGN KEY (`ersteller_external_id`) REFERENCES `users` (`external_id`),
  CONSTRAINT `kampagnen_sender_external_id_foreign` FOREIGN KEY (`sender_external_id`) REFERENCES `users` (`external_id`),
  CONSTRAINT `kampagnen_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `kommentar_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `kommentar_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `korrespondenz_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `korrespondenz_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `versandart` enum('email','brief') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `betreff` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `versendet_at` timestamp NULL DEFAULT NULL,
  `empfaenger` json NOT NULL,
  `absender` json DEFAULT NULL,
  `cc` json DEFAULT NULL,
  `bcc` json DEFAULT NULL,
  `status` enum('entwurf','in_bearbeitung','versandbereit','in_versand','abgeschlossen','fehler') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cc_actual` json DEFAULT NULL,
  `bcc_actual` json DEFAULT NULL,
  `empfaenger_actual` json DEFAULT NULL,
  `absender_actual` json DEFAULT NULL,
  `brief_datum` date DEFAULT NULL,
  `s3_content_ref` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_type` enum('outgoing','incoming','uploaded') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `message_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `brief_absender_type` enum('makler','kunde') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `include_full_history` tinyint(1) NOT NULL DEFAULT '0',
  `email_meta_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `korrespondenz_elemente_s3_content_ref_unique` (`s3_content_ref`),
  KEY `korrespondenz_elemente_message_id_index` (`message_id`),
  KEY `korrespondenz_elemente_email_meta_id_foreign` (`email_meta_id`),
  CONSTRAINT `korrespondenz_elemente_email_meta_id_foreign` FOREIGN KEY (`email_meta_id`) REFERENCES `email_metas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `kunden`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `kunden` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_external_id` bigint NOT NULL,
  `external_updated_at` datetime DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `informal` tinyint(1) DEFAULT NULL,
  `salutation_type` enum('','Herr','Frau','Firma') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `kunden_external_id_unique` (`external_id`),
  KEY `user_external_id_index` (`user_external_id`),
  KEY `kunden_user_external_id_index` (`user_external_id`),
  KEY `kunden_external_id_index` (`external_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `loesch_kommentar_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `loesch_kommentar_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `element_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `element_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `content` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  KEY `loesch_kommentar_elemente_element_type_element_id_index` (`element_type`,`element_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `mahnung_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mahnung_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `versandart` enum('email','brief') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `betreff` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `versendet_at` timestamp NULL DEFAULT NULL,
  `empfaenger` json NOT NULL,
  `absender` json DEFAULT NULL,
  `cc` json DEFAULT NULL,
  `bcc` json DEFAULT NULL,
  `status` enum('entwurf','in_bearbeitung','versandbereit','in_versand','abgeschlossen','fehler') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cc_actual` json DEFAULT NULL,
  `bcc_actual` json DEFAULT NULL,
  `empfaenger_actual` json DEFAULT NULL,
  `absender_actual` json DEFAULT NULL,
  `brief_datum` date DEFAULT NULL,
  `brief` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `s3_content_ref` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `include_full_history` tinyint(1) NOT NULL DEFAULT '0',
  `email_meta_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mahnung_elemente_s3_content_ref_unique` (`s3_content_ref`),
  KEY `mahnung_elemente_email_meta_id_foreign` (`email_meta_id`),
  CONSTRAINT `mahnung_elemente_email_meta_id_foreign` FOREIGN KEY (`email_meta_id`) REFERENCES `email_metas` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `vorgang_id` bigint unsigned NOT NULL,
  `timeline_eintrag_id` bigint unsigned DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_user_id_foreign` (`user_id`),
  KEY `notifications_vorgang_id_user_id_index` (`vorgang_id`,`user_id`),
  KEY `notifications_timeline_eintrag_id_foreign` (`timeline_eintrag_id`),
  CONSTRAINT `notifications_timeline_eintrag_id_foreign` FOREIGN KEY (`timeline_eintrag_id`) REFERENCES `timeline_eintraege` (`id`),
  CONSTRAINT `notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `notifications_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sparten`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sparten` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abkuerzung` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sync_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sync_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `target` enum('firmen','kunden','mails','users','vertraege','schaeden','deleted_firmen','deleted_kunden','deleted_users','deleted_vertraege') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `last_synced_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_synced_timestamp` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `sync_logs_user_id_foreign` (`user_id`),
  KEY `sync_logs_last_synced_id_index` (`last_synced_id`),
  KEY `sync_logs_last_synced_timestamp_index` (`last_synced_timestamp`),
  KEY `sync_logs_target_created_at_index` (`target`,`created_at`),
  CONSTRAINT `sync_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `systemkommentar_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `systemkommentar_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch_id` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `family_hash` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT '1',
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tag` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  KEY `telescope_entries_tags_entry_uuid_tag_index` (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `timeline_eintraege`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `timeline_eintraege` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorgang_id` bigint unsigned NOT NULL,
  `element_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `element_id` bigint unsigned NOT NULL,
  `owner_id` bigint unsigned DEFAULT NULL,
  `ersteller_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `timeline_eintraege_vorgang_id_foreign` (`vorgang_id`),
  KEY `timeline_eintraege_element_type_element_id_index` (`element_type`,`element_id`),
  KEY `timeline_eintraege_ersteller_type_ersteller_id_index` (`owner_id`),
  KEY `timeline_eintraege_ersteller_id_foreign` (`ersteller_id`),
  CONSTRAINT `timeline_eintraege_ersteller_id_foreign` FOREIGN KEY (`ersteller_id`) REFERENCES `users` (`id`),
  CONSTRAINT `timeline_eintraege_owner_id_foreign` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`),
  CONSTRAINT `timeline_eintraege_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `has_logo_in_letter` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `has_email_signature_delimiter` tinyint(1) NOT NULL DEFAULT '0',
  `date_format` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'dynamisch',
  `vorgangstitel_source` enum('vorgangstyp','vorlage') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'vorgangstyp',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_settings_user_id_unique` (`user_id`),
  CONSTRAINT `user_settings_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `external_id` bigint NOT NULL,
  `firma_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_external_id_unique` (`external_id`),
  KEY `users_firma_id_foreign` (`firma_id`),
  CONSTRAINT `users_firma_id_foreign` FOREIGN KEY (`firma_id`) REFERENCES `firmen` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `verknuepfung_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `verknuepfung_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `aktion` enum('erstellt','entfernt') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `vorgang_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `verknuepfung_elemente_vorgang_id_foreign` (`vorgang_id`),
  CONSTRAINT `verknuepfung_elemente_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vertraege`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vertraege` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `external_id` bigint NOT NULL,
  `vertragsnummer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `kfz_kennzeichen` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_external_id` bigint DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `sparte_external_id` bigint DEFAULT NULL,
  `gesellschaft_external_id` bigint DEFAULT NULL,
  `bafin_gesellschaft_external_id` bigint DEFAULT NULL,
  `kunde_external_id` bigint NOT NULL,
  `external_updated_at` datetime DEFAULT NULL,
  `external_schaden` tinyint unsigned DEFAULT NULL,
  `status_external_id` tinyint unsigned DEFAULT NULL,
  `verwahrstelle` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `firma_external_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `vertraege_external_id_unique` (`external_id`),
  KEY `vertraege_user_id_foreign` (`user_id`),
  KEY `vertraege_user_external_id_index` (`user_external_id`),
  KEY `vertraege_firma_external_id_index` (`firma_external_id`),
  KEY `vertraege_kunde_external_id_index` (`kunde_external_id`),
  KEY `vertraege_external_id_index` (`external_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vertrag_kunde`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vertrag_kunde` (
  `vertrag_external_id` bigint unsigned NOT NULL,
  `kunde_external_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`kunde_external_id`,`vertrag_external_id`),
  KEY `vertrag_kunde_copy_vertrag_external_id_index` (`vertrag_external_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgaenge`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgaenge` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorgangsnummer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `vorgangs_gruppe` bigint unsigned DEFAULT NULL,
  `vorgaenger_id` bigint unsigned DEFAULT NULL,
  `titel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('entwurf','offen','erinnerung_1','erinnerung_2','mahnung_1','mahnung_2','erledigt') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vorgangsart` enum('undefined','vorgangsgruppe','aufgabe','korrespondenz_email','korrespondenz_brief','dunkelverarbeitet') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'undefined',
  `is_wichtig` tinyint(1) NOT NULL DEFAULT '0',
  `service_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'unknown',
  `owner_id` bigint unsigned NOT NULL,
  `ersteller_id` bigint unsigned DEFAULT NULL,
  `faellig_at` date DEFAULT NULL,
  `kunde_id` bigint unsigned DEFAULT NULL,
  `gesellschaft_id` bigint unsigned DEFAULT NULL,
  `vertriebsweg_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `sparte_id` bigint unsigned DEFAULT NULL,
  `vorgang_typ_id` bigint unsigned DEFAULT NULL,
  `is_initialized` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `vorgaenge_user_id_foreign` (`owner_id`),
  KEY `vorgaenge_faellig_at_index` (`faellig_at`),
  KEY `vorgaenge_sparte_id_foreign` (`sparte_id`),
  KEY `vorgaenge_vorgang_typ_id_foreign` (`vorgang_typ_id`),
  KEY `vorgaenge_vorgangs_gruppe_foreign` (`vorgangs_gruppe`),
  KEY `vorgaenge_vorgaenger_id_foreign` (`vorgaenger_id`),
  KEY `vorgaenge_ersteller_id_foreign` (`ersteller_id`),
  KEY `vorgaenge_vorgangsnummer_index` (`vorgangsnummer`),
  KEY `vorgaenge_kunde_id_index` (`kunde_id`),
  CONSTRAINT `vorgaenge_ersteller_id_foreign` FOREIGN KEY (`ersteller_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `vorgaenge_owner_id_foreign` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `vorgaenge_sparte_id_foreign` FOREIGN KEY (`sparte_id`) REFERENCES `sparten` (`id`),
  CONSTRAINT `vorgaenge_vorgaenger_id_foreign` FOREIGN KEY (`vorgaenger_id`) REFERENCES `vorgaenge` (`id`),
  CONSTRAINT `vorgaenge_vorgang_typ_id_foreign` FOREIGN KEY (`vorgang_typ_id`) REFERENCES `vorgang_typen` (`id`) ON DELETE CASCADE,
  CONSTRAINT `vorgaenge_vorgangs_gruppe_foreign` FOREIGN KEY (`vorgangs_gruppe`) REFERENCES `vorgaenge` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_aenderung_faelligkeit_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_aenderung_faelligkeit_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorheriger_wert` date NOT NULL,
  `neuer_wert` date NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_aenderung_prioritaet_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_aenderung_prioritaet_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorheriger_wert` tinyint(1) NOT NULL,
  `neuer_wert` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_aenderung_status_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_aenderung_status_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorheriger_wert` enum('offen','entwurf','wartend','erinnerung_1','erinnerung_2','mahnung_1','mahnung_2','erledigt') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `neuer_wert` enum('offen','entwurf','wartend','erinnerung_1','erinnerung_2','mahnung_1','mahnung_2','erledigt') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_aenderung_titel_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_aenderung_titel_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorheriger_wert` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `neuer_wert` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_aenderung_user_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_aenderung_user_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `author_id` bigint unsigned DEFAULT NULL,
  `assigned_user_id` bigint unsigned NOT NULL,
  `aktion` enum('hinzugefuegt','entfernt') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `task` enum('bearbeiter','beobachter') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_aenderung_vertrag_elemente`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_aenderung_vertrag_elemente` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `author_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_bearbeiter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_bearbeiter` (
  `vorgang_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  UNIQUE KEY `vorgang_bearbeiter_vorgang_id_user_id_unique` (`vorgang_id`,`user_id`),
  KEY `vorgang_bearbeiter_user_id_foreign` (`user_id`),
  CONSTRAINT `vorgang_bearbeiter_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `vorgang_bearbeiter_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_beobachter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_beobachter` (
  `vorgang_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  UNIQUE KEY `vorgang_beobachter_vorgang_id_user_id_unique` (`vorgang_id`,`user_id`),
  KEY `vorgang_beobachter_user_id_foreign` (`user_id`),
  CONSTRAINT `vorgang_beobachter_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `vorgang_beobachter_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_participants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_participants` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorgang_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `participant_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `vorgang_participants_vorgang_id_user_id_participant_type_unique` (`vorgang_id`,`user_id`,`participant_type`),
  KEY `vorgang_participants_user_id_foreign` (`user_id`),
  CONSTRAINT `vorgang_participants_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `vorgang_participants_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_typen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_typen` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `titel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `empfaenger_typ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_email` tinyint(1) NOT NULL,
  `is_brief` tinyint(1) NOT NULL,
  `external_support_assignment_subject_id` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `vorgang_typen_titel_empfaenger_typ_unique` (`titel`,`empfaenger_typ`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_verknuepfung`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_verknuepfung` (
  `von_id` bigint unsigned NOT NULL,
  `zu_id` bigint unsigned NOT NULL,
  UNIQUE KEY `vorgang_verknuepfung_von_id_zu_id_unique` (`von_id`,`zu_id`),
  KEY `vorgang_verknuepfung_zu_id_foreign` (`zu_id`),
  CONSTRAINT `vorgang_verknuepfung_von_id_foreign` FOREIGN KEY (`von_id`) REFERENCES `vorgaenge` (`id`),
  CONSTRAINT `vorgang_verknuepfung_zu_id_foreign` FOREIGN KEY (`zu_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorgang_vertrag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorgang_vertrag` (
  `vorgang_id` bigint unsigned NOT NULL,
  `vertrag_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  KEY `vorgang_vertrag_vorgang_id_foreign` (`vorgang_id`),
  KEY `vorgang_vertrag_vertrag_id_foreign` (`vertrag_id`),
  CONSTRAINT `vorgang_vertrag_vertrag_id_foreign` FOREIGN KEY (`vertrag_id`) REFERENCES `vertraege` (`id`),
  CONSTRAINT `vorgang_vertrag_vorgang_id_foreign` FOREIGN KEY (`vorgang_id`) REFERENCES `vorgaenge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorlagen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorlagen` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ersteller_id` bigint unsigned DEFAULT NULL,
  `usage_by_owner_only` tinyint(1) NOT NULL DEFAULT '0',
  `vorlage_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `vorlage_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_type_and_id` (`vorlage_id`,`vorlage_type`),
  KEY `vorlagen_ersteller_id_foreign` (`ersteller_id`),
  KEY `vorlagen_vorlage_type_vorlage_id_index` (`vorlage_type`,`vorlage_id`),
  CONSTRAINT `vorlagen_ersteller_id_foreign` FOREIGN KEY (`ersteller_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorlagen_brief`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorlagen_brief` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorgang_typ_id` bigint unsigned DEFAULT NULL,
  `empfaenger_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'gesellschaft',
  `sender_typ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `informal_content` longtext COLLATE utf8mb4_unicode_ci,
  `formal_content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `vorlagen_brief_vorgang_typ_id_foreign` (`vorgang_typ_id`),
  CONSTRAINT `vorlagen_brief_vorgang_typ_id_foreign` FOREIGN KEY (`vorgang_typ_id`) REFERENCES `vorgang_typen` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorlagen_kampagne_brief`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorlagen_kampagne_brief` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `formal_content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `informal_content` longtext COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorlagen_kampagne_mail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorlagen_kampagne_mail` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `formal_content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `formal_subject` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `informal_content` longtext COLLATE utf8mb4_unicode_ci,
  `informal_subject` longtext COLLATE utf8mb4_unicode_ci,
  `attachments` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vorlagen_mail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vorlagen_mail` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `vorgang_typ_id` bigint unsigned DEFAULT NULL,
  `empfaenger_types` json DEFAULT NULL,
  `cc` json NOT NULL,
  `bcc` json NOT NULL,
  `informal_content` longtext COLLATE utf8mb4_unicode_ci,
  `formal_content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `formal_subject` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `informal_subject` longtext COLLATE utf8mb4_unicode_ci,
  `attachments` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vorlagen_vorgang_typ_id_foreign` (`vorgang_typ_id`),
  CONSTRAINT `vorlagen_vorgang_typ_id_foreign` FOREIGN KEY (`vorgang_typ_id`) REFERENCES `vorgang_typen` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'2014_10_12_000000_create_firmen_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2014_10_12_000001_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2021_05_12_124058_create_vorgaenge_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2021_05_12_132043_create_kunden_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2021_05_18_061833_create_gesellschaften_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2021_05_18_061852_create_sparten_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2021_05_18_064316_create_vertraege_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2021_05_19_090255_create_timeline_eintraege_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2021_05_19_091109_create_korrespondenzen_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2021_05_20_132549_create_verknuepfung_elemente_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2021_05_20_132602_create_vorgaenge_verknuepfungen_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2021_05_27_103141_add_is_wichtig_col',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2021_05_28_073600_create_vorgang_vertrag_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2021_05_28_133038_create_alter_korrespondenzen_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2021_06_03_121736_create_status_aenderungen_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2021_06_08_081823_update_sparten_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2021_06_14_140357_update_vorgaenge_table_faellig_at_not_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2021_06_16_082306_update_vorgaenge_table_add_sparte_id',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2021_06_30_133425_korrespondenz_content_as_json_and_html',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2021_07_14_070354_adds_bearbeiter_and_beobachter_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2021_07_15_125918_adds_fields_to_kunde_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2021_07_15_131701_adds_fields_to_vertraege_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2021_07_15_135425_create_vorgang_typen_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2021_07_23_082649_adds_cc_and_bcc',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2021_07_23_085533_adds_status_and_statustext',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2021_07_30_172352_alter_kunden_add_email',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2021_08_09_141534_create_files_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2021_08_12_121011_adds_user_anederung_timeline_element',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'2021_08_18_091536_adds_status_and_schaden_to_vertraege',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'2021_08_27_122724_add_real_empfaenger_cc_bcc',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2021_09_03_091159_update_enum_status_in_vorgaenge',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2021_09_07_142803_sparten_table_add_abkuerzung',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2021_09_08_144405_add_vorgang_gruppe_to_vorgaenge',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2021_09_10_105939_adds_abkuerzung_to_gesellschaft',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2021_09_13_102859_adds_kfz_kennzeichen_to_vertrag',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2021_09_14_134105_add_brief_datum_to_korrespondenz_elemente',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2021_09_23_123407_add_brief_to_korrespondenz',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2021_10_18_125701_create_vorlagen_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2021_10_21_082416_rename_vorlagen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2021_10_21_135214_add_brief_vorlagen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2021_10_27_113133_adds_softdelete_to_vorlagen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2021_11_04_132511_vorlagen_to_morphable_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2021_11_15_152815_vorlagen_type_camel_case',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (46,'2021_11_16_164111_vorlagen_remove_sparte_id',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2021_11_18_101945_vorlagen_mail_cc_bcc',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2021_11_18_104400_empfaenger_types',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2021_11_18_133513_delete_existing_mail_vorlagen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2021_11_18_134236_empfaenger_types_as_json',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2021_11_24_162947_add_informal_to_kunden',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2021_12_08_144032_vorlage_html_to_json',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2021_12_08_165201_add_salutation_type_to_kunden',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2021_12_16_123037_add_unique_identifier_on_vorlagen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2021_12_20_104814_create_table_user_settings',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2022_01_05_141645_create_aufgaben_elemente_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2022_01_14_101632_add_soft_delete_to_users',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2022_01_21_143529_rename_aufgaben_kommentare',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2022_01_25_085319_add_attachments_to_vorlagen_mail',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2022_02_02_181628_files_make_mimetype_optional',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2022_02_25_163918_add_default_value_for_attachement_in_vorlagen_mail',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2022_03_01_151057_create_dokument_typen_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2022_03_15_085828_create_mahnung_and_erinnerung',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2022_03_18_125348_create_externe_korrespondenz_elemente',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2022_03_21_102229_add_attachement_flag_to_externe_korrespondenz_elemente',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2022_04_01_074940_create_gespraechsnotiz_elemente',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2022_04_01_150919_mahnung_und_erinnerungen_content_and_content_json_stored_in_s3',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2022_04_06_080103_fix_externe_korrespondenzen_morph_type',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2022_04_14_093617_add_soft_deletes_to_kunde_firma_vertrag',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2022_05_03_104858_create_folge_vorgang',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2022_05_03_134242_create_bulk_changes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2022_05_10_104407_add_new_vorgangtypen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2022_05_10_181309_make_external_id_unique',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2022_05_11_150845_user_add_status',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2022_05_12_140525_add_external_updated_to_firma',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2022_05_13_144808_add_versender_in_bcc',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2022_05_17_121600_remove_duplicated_vorgang_typen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2022_05_17_134728_add_type_to_korrespondenz',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2022_05_18_104202_remove_invalid_imported_vorlagen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2022_06_07_122553_create_vertrag_kunde_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (81,'2022_06_13_170644_add_resyncable_attribute_to_firmen',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2022_06_22_115433_remove_demv_vorlagen_for_resync',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2022_06_23_145741_external_updated_date_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2022_07_13_100758_update_korrespondenz_elemente_absender_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2022_07_20_115758_update_erinnerung_elemente_absender_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2022_08_03_092537_add_missing_vorgangtyp',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2022_08_03_114510_alter_korrespondenz_add_brief_absender',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2022_08_03_150217_rename_vorgangtyp_dynamikablehnung',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (89,'2022_10_12_102353_add_last_login_to_users',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2022_10_14_115253_update_korrespondenz_elemente_drop_brief',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2018_08_08_100000_create_telescope_entries_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2022_10_11_162212_alter_vorgaenge_add_ersteller_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (94,'2022_10_13_155849_alter_timeline_eintraege_add_owner_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (95,'2022_12_14_100007_add_vorgangsart_to_vorgaenge_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (96,'2022_12_15_095425_set_initial_vorgangsart_values_in_vorgaenge_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (97,'2023_01_10_152625_add_vorgang_typ',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (98,'2023_01_19_112944_update_vertrag_kunde_add_kunde_external_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (99,'2023_01_20_103100_update_vertrag_kunde_drop_kunde_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (100,'2023_01_23_132020_add_kunde_add_firma_external_id_user_external_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (101,'2023_02_02_170600_alter_vorgaenge_modify_vorgangsart_add_dunkelverarbeitet',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (102,'2023_02_21_143244_add_index_to_external_user_id_on_kunde',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (103,'2023_03_03_175309_alter_table_usersettings_signature_delimiter',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (104,'2023_03_10_090322_alter_table_vorgang_vorgangsnummer_not_unique',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (105,'2023_03_15_110723_alter_table_vertraege_drop_user_and_foreign',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (106,'2023_03_15_115741_alter_vertrag_kunde_add_external_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (107,'2023_03_15_120810_alter_vertrag_kunde_delete_kunde_external_foreign',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (108,'2023_03_15_120813_migrate_data_vertrag_kunde',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (109,'2023_03_22_133016_alter_vertrag_kunde_add_index',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (110,'2023_03_22_133028_alter_vertrag_add_user_index_external',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (111,'2023_03_22_173543_alter_vertrag_kunde_vertrag_id_nullable',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (112,'2023_03_23_120430_alter_vertrag_kunde_drop_kunde_external_foreign',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (113,'2023_03_29_183135_create_permission_tables',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (114,'2023_03_30_122422_alter_kunde_user_external_index_not_nullable',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (116,'2023_03_30_164736_update_kunde_drop_user_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (117,'2023_03_30_164856_update_kunde_drop_firma_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (118,'2023_04_21_104347_alter_table_vorlagen_brief_empfaenger_required',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (119,'2023_04_26_151612_alter_korrespondenz_add_column_mail_identifier',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (120,'2023_04_27_125851_create_sync_logs',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (121,'2023_05_12_141319_sync_logs_add_targets',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (122,'2023_05_16_152832_update_korrespondenz_elemene_fill_empty_actual_fields',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (123,'2023_05_23_142800_sync_logs_add_targets',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (124,'2023_05_23_151440_update_sync_logs_add_targets',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (125,'2023_05_25_112253_alter_vertraege_add_external_firma_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (126,'2023_05_25_151902_alter_vertraege_set_external_firma_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (127,'2023_05_26_135211_alter_vertraege_remove_firma_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (128,'2023_05_31_141156_alter_vertraege_firma_external_id_not_nullable',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (129,'2023_06_01_152603_alter_vorgaenge_add_column_service_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (130,'2023_06_05_114254_create_bezuege_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (131,'2023_06_12_104626_create_user_mail_import_logs',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (132,'2023_06_15_131925_update_users_undelete_stornierte_user',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (133,'2023_06_26_172219_timeline_elemente_nullable_swap_content_cols',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (134,'2023_07_03_143122_update_firmen_drop_full_sync_required',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (135,'2023_07_18_131616_drop_mail_import_logs',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (136,'2023_07_19_151235_create_table_vorgang_access_logs',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (137,'2023_07_27_132122_alter_table_vorgang_accesslogs_add_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (138,'2023_07_28_142036_create_loesch_kommentar_elemente_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (139,'2023_08_16_131038_add_firmenkuerzel_to_firmen',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (140,'2023_08_25_145106_add_option_to_include_full_history_for_korrespondenz',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (141,'2023_08_30_142210_alter_vorlagen_content_string',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (143,'2023_09_08_134643_alter_table_files_add_column_kundenakte_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (144,'2023_09_12_100630_alter_table_vertraege_add_kunden_index',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (145,'2023_09_12_143251_create_vorgang_aenderung_vertrag_elemente',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (146,'2023_09_12_151030_create_notifications_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (147,'2023_09_12_172240_remove_access_log',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (148,'2023_09_19_095910_fix_check_up_vorgang_typ',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (149,'2023_09_26_122350_combine_beobachter_and_bearbeiter_into_participant',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (150,'2023_10_23_151343_add_complete_enum_to_bulk_changes',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (151,'2023_10_25_174152_alter_table_kunden_add_title',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (152,'2023_11_13_150414_alter_table_vorgaenge_faellig_at_nullable',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (153,'2023_11_20_120208_alter_korrespondenz_elemente_add_index_message_id',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (154,'2023_11_20_124213_alter_vorgaenge_add_index_vorgangsnummer',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (155,'2023_11_30_163044_alter_table_vertrag_kunde_various_fixes',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (156,'2023_12_01_125014_alter_table_sync_logs_add_index',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (157,'2023_12_05_150735_update_kommentar_elemente_content_to_longtext',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (158,'2024_01_02_155117_add_two_new_vorgang_typen',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (159,'2024_01_18_150934_alter_files_add_status_field',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (160,'2024_01_18_153337_alter_files_change_status_field_default',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (161,'2024_02_08_171337_add_vorgangstyp_ausschluss_dynamik',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (162,'2024_02_16_124945_alter_files_add_temp_data_field',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (163,'2024_02_19_133051_alter_notifications_add_index_vorgang_user',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (164,'2024_02_20_133602_alter_notifications_add_timeline_eintrag_column',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (165,'2024_02_21_171938_alter_korrespondenz_elemente_change_status_enum',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (166,'2024_03_06_131804_remove_permission_vorgaenge_verwalten',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (167,'2024_03_22_113000_alter_table_kunden_add_external_id_index',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (168,'2024_03_22_113200_alter_table_vertraege_add_external_id_index',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (169,'2024_04_15_092731_add_vorgang_typ',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (170,'2024_04_24_081237_add_bezuege_referenz_index',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (171,'2024_04_24_100004_add_vorgaenge_kunde_id_index',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (172,'2024_05_03_122614_add_features_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (173,'2024_05_06_140024_add_column_date_format_to_user_settings_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (174,'2024_05_21_132504_alter_vorgang_typen_empfaenger_typ_widerruf',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (175,'2024_05_27_130000_alter_korrespondenz_elemente_change_status_enum',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (176,'2024_05_31_111750_alter_features_add_active_for_firmen',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (177,'2024_05_31_115039_alter_features_rename_active_column',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (178,'2024_06_03_163456_alter_erinnerungen_mahnung_remove_counter',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (179,'2024_06_05_100721_alter_table_timeline_eintraege_owner_id_nullable',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (180,'2024_06_06_100721_create_systemkommentar_elemente_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (181,'2024_06_25_144957_alter_table_gesellschaften_add_column_is_pool',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (182,'2024_06_26_152652_alter_vorgang_typen_add_anforderung_freigabeerklaerung',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (191,'2024_06_28_094226_sparten_merge',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (192,'2024_07_17_080329_alter_table_vertraege_add_columns_bafin_and_verwahrstelle',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (193,'2024_07_24_074439_change_empfaenger_of_vorgangstypen',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (194,'2024_07_26_125717_alter_table_vorgaenge_add_column_vertriebsweg',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (195,'2024_08_01_113520_alter_table_vertraege_rename_column_bafin_external_id',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (196,'2024_07_18_131734_add_kampagnen_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (197,'2024_07_18_131742_add_kampagne_messages_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (198,'2024_08_28_161708_sparten_merge',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (199,'2024_09_03_143437_add_vorlagen_kampagne_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (200,'2024_09_12_161335_rename_vorlagen_kampagne_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (201,'2024_09_12_161523_add_vorlagen_kampagne_brief_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (202,'2024_09_18_131734_alter_kampagnen_table_split_formal_informal_betreff_content',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (204,'2024_10_23_141614_alter_table_vorgaenge_add_is_initialized',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (205,'2024_10_07_123443_alter_table_kampagnen_add_attachments_column',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (206,'2024_10_28_152520_alter_kampagne_message_table_add_id_content_and_betreff',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (222,'2024_11_07_080750_alter_table_kampagne_messages',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (223,'2024_11_12_165103_alter_kampagne_table_add_vorgang_id',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (224,'2024_11_14_165103_alter_kampagne_messages_table_add_vorgang_id',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (225,'2024_11_20_125303_create_email_meta_table',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (226,'2024_11_20_125455_alter_table_korrespondenz_elemente_add_email_meta_id',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (227,'2024_11_20_125520_alter_table_kampagne_messages_add_email_meta_id',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (228,'2024_11_29_110407_create_file_model_table',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (229,'2024_11_29_110717_create_file_models_table',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (231,'2024_11_29_110717_create_model_has_files_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (232,'2024_11_29_113825_migrate_files_data_into_model_has_files_table',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (234,'2024_11_28_112736_alter_kampagne_message_drop_versandstatus',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (235,'2024_12_02_112747_drop_model_has_files_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (236,'2024_12_03_144226_create_job_batches_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (238,'2024_12_04_132816_alter_table_files_add_column_s3_name',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (239,'2024_12_06_120227_alter_kampagne_table_update_status_enum',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (240,'2024_11_12_153831_add_vorgangstitel_to_usersettings',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (241,'2024_12_09_113921_alter_kampagne_message_table_add_timestamps_columns',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (242,'2024_12_02_105943_alter_kampagne_table_add_versendet_columns',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (243,'2024_12_11_152436_alter_kampagne_table_update_status_enum_add_geplant',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (244,'2024_12_17_204429_alter_table_kampagnen_change_content_column_type_to_medium_text',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (245,'2025_01_06_150425_alter_table_email_metas_add_column_mailer_id',19);
insert into vorgang_typen (id, titel, empfaenger_typ, is_email, is_brief, external_support_assignment_subject_id, created_at, updated_at)
values  (152, 'Anforderung Freigabeerklärung', 'gesellschaft', 1, 0, 1, '2024-07-02 14:07:26', '2024-07-02 14:07:26'),
        (151, 'BiPRO - Dokument zum Vertrag fehlt', 'gesellschaft', 1, 0, null, '2024-05-16 15:47:21', '2024-05-16 15:47:21'),
        (150, 'Ausschluss Dynamik (Makler)', 'gesellschaft', 0, 1, null, '2024-05-16 15:47:19', '2024-05-16 15:47:19'),
        (149, 'Ausschluss Dynamik (Kunde)', 'gesellschaft', 0, 1, null, '2024-05-16 15:47:19', '2024-05-16 15:47:19'),
        (148, 'Rückinfo nach VR-Wechsel', 'gesellschaft', 1, 0, null, '2024-05-16 15:47:19', '2024-05-16 15:47:19'),
        (147, 'Zustimmungserklärung Gläubiger', 'gesellschaft', 1, 0, null, '2024-05-16 15:47:19', '2024-05-16 15:47:19'),
        (146, 'BiPRO - Freischaltung an DEMV', 'gesellschaft', 1, 0, null, '2024-05-16 15:47:02', '2024-05-16 15:47:02'),
        (144, 'Eigentümerwechsel Wohngebäudeversicherung', 'gesellschaft', 1, 0, null, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (143, 'Ausschluss Dynamik', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (142, 'Anforderung Einzelschadenaufstellung', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (141, 'Anforderung Zweitschrift', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (140, 'Versicherungsnehmerwechsel', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (139, 'Zusendung Dauerzulagenantrag Riester (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (138, 'Rabattübertragung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (137, 'KFZ-Erinnerung Antrag nach eVB-Nummer (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (136, 'Dynamikablehnung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (135, 'Jahresmitteilung - Check BU', 'gesellschaft', 1, 0, null, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (134, 'Papier Postversand abstellen', 'vertriebsweg', 1, 0, 20, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (133, 'Antrag per Dunkelverarbeitung eingereicht', 'vertriebsweg', 1, 0, 10, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (132, 'Kündigung Altmaklervertrag', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (131, 'Risikofortfall', 'gesellschaft', 1, 1, 19, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (130, 'Einmaliger Dynamik Ausschluss', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:28', '2022-10-24 16:02:28'),
        (129, 'Depotauflösung', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (128, 'Rentenbescheid anfordern', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (127, 'Versand von Originalbelegen', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (126, 'Antwort-Schreiben', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (125, 'Angebot einholen', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (124, 'Änderungsangebot anfordern', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (123, 'Arbeitgeber-Anschreiben bAV', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (121, 'Briefkopf/Anschreiben/Korrespondenz', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (120, 'Allgemeine Anfrage', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (119, 'Vertragsspiegel zusenden', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (118, 'Bestandsübertragung', 'gesellschaft', 0, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (117, 'Verkauf versichertes Gebäude', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (116, 'Zusammenführung Vermittlernummern für GDV und BiPrO', 'vertriebsweg', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (115, 'DEMV - Hilfe für Courtageumzug bei DEMV beantragen', 'demv', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (114, 'GDV Datenlieferung zum Makler', 'vertriebsweg', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (113, 'CSV/Excel-Provisionsdaten fehlen', 'vertriebsweg', 1, 0, 11, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (112, 'Freistellungsauftrag', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (111, 'SEPA-Mandat versenden', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (110, 'KFZ-Anforderung Grüne Karte', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (109, 'KFZ-Meldung Kilometerstand', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (108, 'Nachmeldung berechtigter Fahrer (Kfz)', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (107, 'Zusendung Gesundheitsfragebogen', 'gesellschaft', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (106, 'Bestandsübertragung zum Maklerpool', 'vertriebsweg', 1, 0, 5, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (105, 'Übermittlung angeforderter Unterlagen', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (104, 'Terminbestätigung', 'gesellschaft', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (103, 'Meldung neuer mitversicherter Personen / Risiken', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (102, 'Erstattung Krankheitskosten', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (101, 'Kindernachversicherung', 'gesellschaft', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (100, 'Login zu tariffair (Barmenia - Adcuri)', 'gesellschaft', 1, 0, 11, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (99, 'Wechsel Trägerunternehmen', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (98, 'Versicherungsnehmerwechsel (bAV)', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (97, 'Anforderung Unterlagen Geburt Kind (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (96, 'Bestätigung Namensänderung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (95, 'Bestätigung Wechsel Bankverbindung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (94, 'Freigabe Maklerpool', 'vertriebsweg', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (93, 'Vertragsaufhebung wegen Doppelversicherung', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (91, 'Check-up Finanzen (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (90, 'Bestandsübertragung Qualitypool', 'vertriebsweg', 1, 0, 5, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (89, 'Ablauf (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (88, 'Beitragsrechnung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (87, 'Nachbearbeitung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (86, 'Antragsversand (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (85, 'Bestandsübertragung - Freigabeerklärung Korrespondenzmakler', 'vertriebsweg', 1, 0, 5, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (84, 'Bestandsübertragung - Freigabeerklärung', 'vertriebsweg', 1, 0, 5, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (83, 'BiPRO - Dokumente fehlen', 'vertriebsweg', 1, 0, 20, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (82, 'Versicherungsschein geprüft (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (81, 'Übertragung betriebliche Altersvorsorge', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (80, 'Postrückläufer (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (79, 'BiPRO - Erstellung Benutzerkonto', 'vertriebsweg', 1, 0, 20, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (78, 'BiPRO - Zusendung Zertifikat', 'vertriebsweg', 1, 0, 20, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (77, 'Erstprämie nicht gezahlt (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (76, 'Vertragsaufhebung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (75, 'Ablehnung Kündigung durch VU (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (74, 'Kündigungsbestätigung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (73, 'Schadenmeldung - Ablehnung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (72, 'Schadenmeldung - Regulierung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (71, 'SEPA-Mandat fehlt (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (70, 'Bankverbindung fehlt (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (69, 'Bankverbindung falsch (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (68, 'Beitragsrückstand (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (67, 'Vertragsänderung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (66, 'Rückmeldung/Nachfrage (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (65, 'Vorläufige Deckungszusage (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (64, 'Antrag Änderungsangebot (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (63, 'Antrag Widerruf (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (62, 'Antrag Nachfrage (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (61, 'Antrag Ablehnung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (60, 'Annahme/Policierung (M-K)', 'kunde', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (59, 'Erstantrag an DEMV', 'gesellschaft', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (58, 'Allgemeine Korrespondenz', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (57, 'Mitteilung: Gerüstaufbau', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (56, 'Schadenkorrespondenz', 'gesellschaft', 1, 0, 2, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (55, 'Zusendung Beratungsdokumentation', 'gesellschaft', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (54, 'Rabattübertragung SFR / TB28', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (53, 'eVB-Nummer (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (52, 'Allgemeine Korrespondenz (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (51, 'Aktualisierung Maklerauftrag (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (50, 'Maklerauftrag (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (49, 'Finanzreport/Vertragsspiegel (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (48, 'Depotauszug (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (47, 'Kündigung (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (46, 'Formular Schadenmeldung (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (45, 'Vertragsunterlagen (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (44, 'Angebot/Leistungsvergleich (M-K)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (43, 'Antragsunterlagen (M-K kurz)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (42, 'Widerruf', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (41, 'Falsche GDV Daten', 'vertriebsweg', 1, 0, 6, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (40, 'Depotauszug', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (39, 'Schadenregulierung (Status)', 'gesellschaft', 1, 0, 2, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (38, 'Schadenrenta', 'gesellschaft', 1, 0, 6, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (37, 'Korrespondenz: Angebot', 'gesellschaft', 1, 0, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (36, 'Beginnverlegung', 'gesellschaft', 1, 1, 6, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (35, 'Bestandsübertragung Korrespondenzmakler', 'vertriebsweg', 1, 0, 5, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (33, 'Änderungsanzeige - Jahresmeldung', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (32, 'Vertragsänderung', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (31, 'Antragsunterlagen (M-K lang)', 'kunde', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (30, 'GDV Datenlieferung zu DEMV', 'vertriebsweg', 1, 0, 6, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (29, 'Fehlende Antragpolicierung', 'vertriebsweg', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (28, 'Vermittlernummer fehlt, Folgeantrag', 'vertriebsweg', 1, 0, 3, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (27, 'Vermittlernr. fehlt, offener Antrag', 'vertriebsweg', 1, 0, 3, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (26, 'Policierung falsch - Reklamation', 'vertriebsweg', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (25, 'Policierung fehlt - Reklamation', 'vertriebsweg', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (24, 'Betreuungswechsel (Kundeninformation)', 'gesellschaft', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (23, 'Courtage zu gering - Reklamation', 'vertriebsweg', 1, 0, 4, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (22, 'Courtage fehlt - Reklamation', 'vertriebsweg', 1, 0, 4, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (21, 'Anfrage Angebot', 'gesellschaft', 1, 0, 18, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (20, 'Anfrage Änderungsangebot', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (19, 'DEMV - Zusendung Kooperationsunterlagen', 'demv', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (18, 'DEMV - Allgemeine Frage', 'demv', 1, 1, null, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (17, 'Schadenmeldung', 'gesellschaft', 1, 1, 2, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (16, 'Allgemeine Anfrage an Gesellschaft', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (15, 'Beitragssenkung', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (14, 'Beitragsfreistellung', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (13, 'Fondsshift und Fondsswitch', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (12, 'Fondsswitch', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (11, 'Fondsshift', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (10, 'Bestandsübertragung und Zusendung Vertragsspiegel', 'vertriebsweg', 1, 0, 5, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (9, 'Kündigung', 'gesellschaft', 1, 1, 19, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (8, 'Zusendung Vertragsspiegel', 'gesellschaft', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (7, 'Anbieterwechsel Riester', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (6, 'Änderung Bezugsrecht', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (5, 'Nachbearbeitung', 'vertriebsweg', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (4, 'Adresswechsel', 'gesellschaft', 1, 1, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (3, 'GDV-Daten fehlen', 'vertriebsweg', 1, 0, 1, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (2, 'Antrag einreichen', 'vertriebsweg', 1, 1, 10, '2022-10-24 16:02:23', '2022-10-24 16:02:23'),
        (1, 'Risikovoranfrage', 'vertriebsweg', 1, 0, 17, '2022-10-24 16:02:23', '2022-10-24 16:02:23');
