<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\Vorgangsart;
use App\Enums\VorgangsStatus;
use App\Models\User;
use App\Models\Vorgang;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Vorgang>
 */
class VorgangFactory extends Factory
{
    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'owner_id' => User::factory(),
            'vorgangsnummer' => fn (array $attributes) => (
                User::find($attributes['owner_id'])->firma->kuerzel . '-' . fake()->randomNumber(6, strict: true)
            ),
            'titel' => fake()->words(3, asText: true),
            'status' => VorgangsStatus::OFFEN,
            'vorgangsart' => Vorgangsart::AUFGABE,
            'service_id' => 'vorgaenge',
            'faellig_at' => now()->addWeeks(2),
        ];
    }
}
