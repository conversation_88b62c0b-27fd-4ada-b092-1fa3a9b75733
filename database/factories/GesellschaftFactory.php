<?php

namespace Database\Factories;

use App\Models\Gesellschaft;
use Illuminate\Database\Eloquent\Factories\Factory;

class GesellschaftFactory extends Factory
{
    protected $model = Gesellschaft::class;

    public function definition(): array
    {
        return [
            'external_id' => $this->faker->unique()->randomNumber(),
            'name' => $this->faker->name(),
            'abkuerzung' => $this->faker->word(),
            'is_pool' => $this->faker->boolean(),
        ];
    }
}
