<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\UserStatus;
use App\Models\Firma;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<User>
 */
class UserFactory extends Factory
{
    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $externalId = 1; // default set to 1 because vorgaenge calls underlings mocking route with this id

        if (User::query()->where('external_id', '=', 1)->exists()) {
            $externalId = $this->faker->randomNumber();
        }

        return [
            'external_id' => $externalId,
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'status' => UserStatus::AKTIV,
            'firma_id' => Firma::factory(),
        ];
    }
}
