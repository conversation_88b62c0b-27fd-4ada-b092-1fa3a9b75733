<?php

namespace Database\Factories;

use App\Models\VorgangTyp;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class VorgangTypFactory extends Factory
{
    protected $model = VorgangTyp::class;

    public function definition(): array
    {
        return [
            'titel' => $this->faker->word(),
            'empfaenger_typ' => $this->faker->randomElement(['vertriebsweg', 'gesellschaft', 'kunde', 'demv']),
            'is_email' => $this->faker->boolean,
            'is_brief' => $this->faker->boolean(),
            'external_support_assignment_subject_id' => $this->faker->randomNumber(),
        ];
    }
}
