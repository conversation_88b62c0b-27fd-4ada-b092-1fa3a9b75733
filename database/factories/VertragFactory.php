<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Vertrag;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Vertrag>
 */
class VertragFactory extends Factory
{
    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'external_id' => fake()->numberBetween(1),
            'vertragsnummer' => fake()->randomLetter() . fake()->numberBetween(10000),
            'kunde_external_id' => fake()->numberBetween(1),
            'firma_external_id' => fake()->numberBetween(1),

        ];
    }
}
