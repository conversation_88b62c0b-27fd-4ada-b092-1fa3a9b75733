<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Firma;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Firma>
 */
class FirmaFactory extends Factory
{
    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'external_id' => fake()->numberBetween(1),
            'name' => fake()->company(),
        ];
    }
}
