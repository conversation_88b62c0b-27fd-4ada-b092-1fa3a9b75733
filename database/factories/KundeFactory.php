<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\SalutationType;
use App\Models\Kunde;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Kunde>
 */
class KundeFactory extends Factory
{
    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'external_id' => fake()->numberBetween(1),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'title' => null,
            'email' => fake()->email(),
            'informal' => fake()->boolean(),
            'salutation_type' => fake()->randomElement(SalutationType::cases()),
        ];
    }
}
