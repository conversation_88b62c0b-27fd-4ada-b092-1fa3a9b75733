<?php

namespace Database\Factories;

use App\Models\Sparte;
use Illuminate\Database\Eloquent\Factories\Factory;

class SparteFactory extends Factory
{
    protected $model = Sparte::class;

    public function definition(): array
    {
        return [
            'id' => $this->faker->randomNumber(),
            'name' => $this->faker->name(),
            'abkuerzung' => $this->faker->word(),
            'display_name' => $this->faker->name(),
        ];
    }
}
