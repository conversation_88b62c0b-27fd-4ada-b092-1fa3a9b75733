<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserAenderungElement;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserAenderungElementFactory extends Factory
{
    protected $model = UserAenderungElement::class;

    public function definition(): array
    {
        return [
            'author_id' => User::factory(),
            'assigned_user_id' => User::factory(),
            'aktion' => $this->faker->randomElement(['hinzugefuegt', 'entfernt']),
            'task' => $this->faker->randomElement(['bearbeiter', 'beoabachter']),
        ];
    }
}
