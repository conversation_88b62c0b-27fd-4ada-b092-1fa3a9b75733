<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Domain\Kommentar\Models\GespraechsnotizElement;
use App\Domain\Kommentar\Models\KommentarElement;
use App\Domain\Kommentar\Models\LoeschKommentarElement;
use App\Domain\Kommentar\Models\SystemkommentarElement;
use App\Domain\Korrespondenz\Models\ErinnerungElement;
use App\Domain\Korrespondenz\Models\ExterneKorrespondenzElement;
use App\Domain\Korrespondenz\Models\KorrespondenzElement;
use App\Domain\Korrespondenz\Models\MahnungElement;
use App\Domain\Verknuepfungen\Models\FolgeVorgangTimelineElement;
use App\Domain\Verknuepfungen\Models\VerknuepfungTimelineElement;
use App\Domain\VorgangAenderungen\Models\FaelligkeitAenderungenElement;
use App\Domain\VorgangAenderungen\Models\StatusAenderungenElement;
use App\Models\TimelineEintrag;
use App\Models\UserAenderungElement;
use App\Models\VertragAenderungenElement;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<TimelineEintrag>
 */
class TimelineEintragFactory extends Factory
{
    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $timelineElements = [
            VertragAenderungenElement::MORPH_TYPE => VertragAenderungenElement::factory(),
            UserAenderungElement::MORPH_TYPE => UserAenderungElement::factory(),
            VerknuepfungTimelineElement::MORPH_TYPE => VerknuepfungTimelineElement::factory(),
            KorrespondenzElement::MORPH_TYPE => KorrespondenzElement::factory(),
            ExterneKorrespondenzElement::MORPH_TYPE => ExterneKorrespondenzElement::factory(),
            LoeschKommentarElement::MORPH_TYPE => LoeschKommentarElement::factory(),
            SystemkommentarElement::MORPH_TYPE => SystemkommentarElement::factory(),
            KommentarElement::MORPH_TYPE => KommentarElement::factory(),
            StatusAenderungenElement::MORPH_TYPE => StatusAenderungenElement::factory(),
            FaelligkeitAenderungenElement::MORPH_TYPE => FaelligkeitAenderungenElement::factory(),
            GespraechsnotizElement::MORPH_TYPE => GespraechsnotizElement::factory(),
            ErinnerungElement::MORPH_TYPE => ErinnerungElement::factory(),
            MahnungElement::MORPH_TYPE => MahnungElement::factory(),
        ];

        $randomKey = array_rand($timelineElements);

        return [
            'element_id' => $timelineElements[$randomKey],
            'element_type' => $randomKey,
        ];
    }
}
