<?php

namespace Database\Factories\Domain\VorgangAenderungen\Models;

use App\Domain\VorgangAenderungen\Models\FaelligkeitAenderungenElement;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class FaelligkeitAenderungenElementFactory extends Factory
{
    protected $model = FaelligkeitAenderungenElement::class;

    public function definition(): array
    {
        $vorher = $this->faker->dateTimeBetween('-1 month', 'now');
        $neu = $this->faker->dateTimeBetween($vorher, '+2 days');

        return [
            'vorheriger_wert' => $vorher,
            'neuer_wert' => $neu,
        ];
    }
}
