<?php

namespace Database\Factories\Domain\VorgangAenderungen\Models;

use App\Domain\VorgangAenderungen\Models\StatusAenderungenElement;
use App\Enums\VorgangsStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class StatusAenderungenElementFactory extends Factory
{
    protected $model = StatusAenderungenElement::class;

    public function definition(): array
    {
        $statuses = VorgangsStatus::cases();
        $vorherigerWert = $this->faker->randomElement($statuses);
        $neuerWert = $this->faker->randomElement(array_filter($statuses, fn($s) => $s !== $vorherigerWert));

        return [
            'vorheriger_wert' => $vorherigerWert,
            'neuer_wert' => $neuerWert,
        ];
    }
}
