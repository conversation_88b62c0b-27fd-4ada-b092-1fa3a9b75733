<?php

namespace Database\Factories\Domain\Kommentar\Models;

use App\Domain\Kommentar\Enums\GespraechType;
use App\Domain\Kommentar\Models\GespraechsnotizElement;
use Illuminate\Database\Eloquent\Factories\Factory;

class GespraechsnotizElementFactory extends Factory
{
    protected $model = GespraechsnotizElement::class;

    public function definition(): array
    {
        $dateFrom = $this->faker->dateTimeBetween('-1 month', 'now');
        $dateTo = $this->faker->dateTimeBetween($dateFrom, '+2 hours');

        return [
            'content' => $this->faker->paragraph(),
            'type' => $this->faker->randomElement(GespraechType::VALUES),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
        ];
    }
}
