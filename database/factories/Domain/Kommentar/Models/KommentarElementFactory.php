<?php

namespace Database\Factories\Domain\Kommentar\Models;

use App\Domain\Kommentar\Models\KommentarElement;
use Illuminate\Database\Eloquent\Factories\Factory;

class KommentarElementFactory extends Factory
{
    protected $model = KommentarElement::class;

    public function definition(): array
    {
        return [
            'content' => $this->faker->word(),
            'element' => $this->faker->randomNumber(),
        ];
    }
}
