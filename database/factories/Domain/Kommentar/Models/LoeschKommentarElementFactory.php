<?php

namespace Database\Factories\Domain\Kommentar\Models;

use App\Domain\Kommentar\Models\KommentarElement;
use App\Domain\Kommentar\Models\LoeschKommentarElement;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class LoeschKommentarElementFactory extends Factory
{
    protected $model = LoeschKommentarElement::class;

    public function definition(): array
    {
        return [
            'content' => $this->faker->paragraph(),
            'user_id' => User::factory(),
            'created_at' => $this->faker->dateTime(),
            'element_id' => KommentarElement::factory(),
            'element_type' => KommentarElement::MORPH_TYPE,
        ];
    }
}
