<?php

namespace Database\Factories\Domain\Korrespondenz\Models;

use App\Domain\Korrespondenz\Models\ExterneKorrespondenzElement;
use Illuminate\Database\Eloquent\Factories\Factory;

class ExterneKorrespondenzElementFactory extends Factory
{
    protected $model = ExterneKorrespondenzElement::class;

    public function definition(): array
    {
        return [
            'uses_attachments' => $this->faker->boolean(),
            'created_at' => $this->faker->dateTime(),
            'updated_at' => $this->faker->dateTime(),
        ];
    }
}
