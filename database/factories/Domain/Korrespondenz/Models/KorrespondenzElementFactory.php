<?php

declare(strict_types=1);

namespace Database\Factories\Domain\Korrespondenz\Models;

use App\Domain\Korrespondenz\Enums\Status;
use App\Domain\Korrespondenz\Enums\Versandart;
use App\Domain\Korrespondenz\Models\KorrespondenzElement;
use App\Enums\EmailType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<KorrespondenzElement>
 */
class KorrespondenzElementFactory extends Factory
{
    protected $model = KorrespondenzElement::class;

    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $uuid = fake()->uuid;

        return [
            'versandart' => Versandart::EMAIL,
            'betreff' => fake()->sentence,
            'empfaenger' => ['email' => fake()->email],
            'cc' => ['email' => fake()->email],
            'bcc' => ['email' => fake()->email],
            'status' => Status::IN_VERSAND,
            's3_content_ref' => "korrespondenz/{$uuid}",
            'email_type' => EmailType::outgoing(),
            'content' => fake()->paragraph,
        ];
    }

    public function brief(): self
    {
        return $this->state(fn () => [
            'versandart' => Versandart::BRIEF,
            'brief_datum' => fake()->date(),
            'empfaenger' => [
                'name' => fake()->name,
                'adresszeile1' => fake()->streetAddress,
                'adresszeile2' => null,
                'plz' => fake()->postcode,
                'stadt' => fake()->city,
                'land' => fake()->country,
            ],
            'cc' => null,
            'bcc' => null,
            'status' => Status::ABGESCHLOSSEN,
            'email_type' => null,
        ]);
    }
}
