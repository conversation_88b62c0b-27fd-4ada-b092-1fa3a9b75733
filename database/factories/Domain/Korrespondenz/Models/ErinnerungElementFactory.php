<?php

namespace Database\Factories\Domain\Korrespondenz\Models;

use App\Domain\Korrespondenz\Enums\Status;
use App\Domain\Korrespondenz\Enums\Versandart;
use App\Domain\Korrespondenz\Models\ErinnerungElement;
use Illuminate\Database\Eloquent\Factories\Factory;

class ErinnerungElementFactory extends Factory
{
    protected $model = ErinnerungElement::class;

    public function definition(): array
    {
        return [
            'versandart' => Versandart::EMAIL,
            'betreff' => '(Erinnerung) ' . $this->faker->sentence(),
            'content' => $this->faker->paragraph(),
            'empfaenger' => [
                [
                    'name' => $this->faker->name(),
                    'email' => $this->faker->email(),
                ]
            ],
            'absender' => [
                'name' => $this->faker->name(),
                'email' => $this->faker->email(),
            ],
            'status' => $this->faker->randomElement(Status::cases()),
            'include_full_history' => false,
        ];
    }
}
