<?php

namespace Database\Factories\Domain\Korrespondenz\Models;

use App\Domain\Korrespondenz\Enums\Status;
use App\Domain\Korrespondenz\Enums\Versandart;
use App\Domain\Korrespondenz\Models\MahnungElement;
use Illuminate\Database\Eloquent\Factories\Factory;

class MahnungElementFactory extends Factory
{
    protected $model = MahnungElement::class;

    public function definition(): array
    {
        return [
            'versandart' => Versandart::EMAIL,
            'betreff' => '1. Mahnung ' . $this->faker->sentence(),
            'content' => $this->faker->paragraph(),
            'empfaenger' => [
                [
                    'name' => $this->faker->name(),
                    'email' => $this->faker->email(),
                ]
            ],
            'absender' => [
                'name' => $this->faker->name(),
                'email' => $this->faker->email(),
            ],
            'status' => $this->faker->randomElement(Status::cases()),
            'include_full_history' => false,
        ];
    }
}
