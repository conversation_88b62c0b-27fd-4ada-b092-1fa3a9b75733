<?php

namespace Database\Factories\Domain\Verknuepfungen\Models;

use App\Domain\Verknuepfungen\Enums\VerknuepfungAktion;
use App\Domain\Verknuepfungen\Models\VerknuepfungTimelineElement;
use App\Models\Vorgang;
use Illuminate\Database\Eloquent\Factories\Factory;

class VerknuepfungTimelineElementFactory extends Factory
{
    protected $model = VerknuepfungTimelineElement::class;

    public function definition(): array
    {
        return [
            'aktion' => $this->faker->randomElement(VerknuepfungAktion::VALUES),
            'vorgang_id' => Vorgang::factory(),
        ];
    }
}
