<?php

namespace Database\Factories\Domain\Verknuepfungen\Models;

use App\Domain\Verknuepfungen\Models\FolgeVorgangTimelineElement;
use App\Models\Vorgang;
use Illuminate\Database\Eloquent\Factories\Factory;

class FolgeVorgangTimelineElementFactory extends Factory
{
    protected $model = FolgeVorgangTimelineElement::class;

    public function definition(): array
    {
        return [
            'vorgaenger_id' => Vorgang::factory(),
            'nachfolger_id' => Vorgang::factory(),
        ];
    }
}
