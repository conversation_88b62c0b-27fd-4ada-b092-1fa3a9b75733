# End-to-End Testing

This directory contains our E2E tests.
We use <a href="https://docs.cypress.io/">Cypress</a> for testing critical paths throughout the system.

## Files & Folders

- `e2e/` contains the list of tests. Each test file should end with `.cy.ts`.
- `fixtures/` contains test fixtures like files, request bodies and login tokens.
- `support/`
  - `commands.ts` contains commands that are added to `cy` to add custom functionality which can be used inside tests.
  - `e2e.ts` is loaded before a test run. Commands and global hooks are loaded here.
- `global.d.ts` contains the global Cypress type. Commands need to be typed here to be used inside test files.

## Get components inside tests

We use `data-test` attributes inside our html to identify elements inside E2E tests.
For defining those names we use <a href="https://en.bem.info/methodology/key-concepts/">BEM Methodology</a>.

```html
<DsButton
  ...
  data-test="button"
  ...
>
  Click Me!
</DsButton>
```

You can get the button above by its data-test attribute.

```ts
cy.getBySel('button').click();
```
