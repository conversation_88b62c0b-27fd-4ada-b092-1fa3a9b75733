Return-Path: <<EMAIL>>
To: <PERSON> <<EMAIL>>
Subject: Example email with attachments
From: nobody <<EMAIL>>
Reply-To: nobody <<EMAIL>>
X-Mailer: Zendesk Mailer
Sender: <EMAIL>
X-Mailer: http://www.phpclasses.org/mimemessage $Revision: 1.63 $ (mail)
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="652b8c4dcb00cdcdda1e16af36781caf"
Message-ID: <<EMAIL>>
Date: Sat, 30 Apr 2005 19:28:29 -0300


--652b8c4dcb00cdcdda1e16af36781caf
Content-Type: multipart/related; boundary="6a82fb459dcaacd40ab3404529e808dc"


--6a82fb459dcaacd40ab3404529e808dc
Content-Type: multipart/alternative; boundary="69c1683a3ee16ef7cf16edd700694a2f"


--69c1683a3ee<PERSON><PERSON><PERSON><PERSON><PERSON>16edd700694a2f
Content-Type: text/plain; charset=ISO-8859-1
Content-Transfer-Encoding: quoted-printable

This is an HTML message. Please use an HTML capable mail program to read
this message.

> First level quote.
> > Second level quote.
> > > Third level quote.

--69c1683a3ee16ef7cf16edd700694a2f
Content-Type: text/html; charset=ISO-8859-1
Content-Transfer-Encoding: quoted-printable

<html>
<head>
<title>Example email with attachments</title>
</head>
<body><h1>Example email with attachments</h1></center>
<hr>
<P>Hello Tester 1,<br><br>
This message is just to let you know that the mail upload works<br><br>
kthxbye,<br>
Nobody</p>
</body>
</html>
--69c1683a3ee16ef7cf16edd700694a2f--

--6a82fb459dcaacd40ab3404529e808dc
Content-Type: image/gif; name="attachment.gif"
Content-Transfer-Encoding: base64
Content-Disposition: inline; filename="attachment.gif"
Content-ID: <ae0357e57f04b8347f7621662cb63855.gif>

R0lGODlhlgAjAPMJAAAAAAAA/y8vLz8/P19fX19f339/f4+Pj4+Pz7+/v///////////////////
/////yH5BAEAAAkALAAAAACWACMAQwT+MMlJq7046827/2AoHYChGAChAkBylgKgKClFyEl6xDMg
qLFBj3C5uXKplVAxIOxkA8BhdFCpDlMK1urMTrZWbAV8tVS5YsxtxmZHBVOSCcW9zaXyNhslVcto
RBp5NQYxLAYGLi8oSwoJBlE+BiSNj5E/PDQsmy4pAJWQLAKJY5+hXhZ2dDYldFWtNSFPiXssXnZR
k5+1pjpBiDMJUXG/Jo7DI4eKfMSmxsJ9GAUB1NXW19jZ2tvc3d7f4OHi2AgZN5vom1kk6F7s6u/p
m3Ab7AOIiCxOyZuBIv8AOeTJIaYQjiR/kKTr5GQNE3pYSjCJ9mUXClRUsLxaZGciC0X+OlpoOuQo
ZKdNJnIoKfnxRUQh6FLG0iLxIoYnJd0JEKISJyAQDodp3EUDC48oDnUY7HFI3wEDRjzycQJVZCQT
Ol7NK+G0qgtkAcOKHUu2rNmzYTVqRMt2bB49bHompSchqg6HcGeANSMxr8sEa2y2HexnSEUTuWri
SSbkYh7BgGVAnhB1b2REibESYaRoBgqIMYx59tFM9AvQffVG49P5NMZkMlHKhJPJb0knmSKZ6kSX
JtbeF3Am7ocok6c7cM7pU5xcXiJJETUz16qPrzEfaFgZpvzn7h86YV5r/1mxXeAUMVyEIpnVUGpN
RlG2ka9b3lP3pm2l6u7P+l/YLj3+RlEHbz1C0kRxSITQaAcilVBMEzmkkEQO8oSOBNg9SN+AX6hV
z1pjgJiAhwCRsY8ZIp6xj1ruqCgeGeKNGEZwLnIwzTg45qjjjjz2GEA5hAUp5JBEFmnkkSCoWEcZ
X8yohZNK1pFGPQS4hx0qNSLJlk9wCQORYu5QiMd7bUzGVyNlRiOHSlpuKdGEItHQ3HZ18beRRyws
YSY/waDTiHf/tWlWUBAJiMJ1/Z0XXU7N0FnREpKM4NChCgbyRDq9XYpOplaKopN9NMkDnBbG+UMC
QwLWIeaiglES6AjGARcPHCWoVAiatcTnGTABZoLPaPG1phccPv366mEvWEFSLnj+2QaonECwcJt/
e1Zw3lJvVMmftBdVNQS3UngLCA85YHIQOy6JO9N4eZW7KJwtOUZmGwOMWqejwVW6RQzaikRHX3yI
osKhDAq8wmnKSmdMwNidSOof9ZG2DoV0RfTVmLFtGmNk+CoZna0HQnPHS3AhRbIeDpqmR09E0bsu
soeaw994z+rwQVInvqLenBftYjLOVphLFHhV9qsnez8AEUbQRgO737AxChjmyANxuEFHSGi7hFCV
4jxLst2N8sRJYU+SHiAKjlmCgz2IffbLI5aaQR71hnkxq1ZfHSfKata6YDCJDMAQwY7wOgzhjxgj
VFQnKB5uX4mr9qJ79pann+VcfcSzsSCd2mw5scqRRvlQ6TgcUelYhu75iPE4JejrsJOFQAG01277
7bjnrvvuvPfu++/ABy887hfc6OPxyCevPDdAVoDA89BHL/301Fdv/fXYZ6/99tx3Pz0FEQAAOw==

--652b8c4dcb00cdcdda1e16af36781caf
Content-Type: text/plain; name="attachment.txt"
Content-Transfer-Encoding: base64
Content-Disposition: attachment; filename="attachment.txt"

VGhpcyBpcyBqdXN0IGEgcGxhaW4gdGV4dCBhdHRhY2htZW50IGZpbGUgbmFtZWQgYXR0YWNobWVudC50eHQgLg==

--652b8c4dcb00cdcdda1e16af36781caf--
