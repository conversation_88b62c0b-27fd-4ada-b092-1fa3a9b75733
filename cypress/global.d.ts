/// <reference types="cypress" />

declare namespace Cypress {
  interface Chainable {
    /**
     * Clears the cache. Is triggered automatically before each test.
     * @example cy.clearCache()
     */
    clearCache(): void;

    /**
     * Refresh the testing database. Is triggered automatically before each test.
     * @example cy.refreshDb()
     */
    refreshDb(): void;

    /**
     * Syncs Firma. Used to get Data from PW Mockup for all Users.
     * @example cy.syncFirma()
     */
    syncFirma(externeFirmenId: int): void;

    /**
     * Mock the next response from Professional works for a specific URL.
     * @example cy.mockPwResponse('get', '/vorgang/mailtemplateexport/list/id/1', '{"status":"success","data": []}')
     */
    mockPwResponse(
      method: string,
      url: string,
      responseBody: string,
      count?: number,
    ): void;

    /**
     * Login the example user. Is triggered automatically before each test.
     * @example cy.login()
     */
    login(): Chainable<AUTWindow>;

    /**
     * Logout the current user.
     * @example cy.logout()
     */
    logout(): Chainable<Response<any>>;

    /**
     * Yields elements with a data-test attribute that match the selector.
     * @example cy.getBySel('basis-info__faellig-at')
     */
    getBySel<K extends keyof HTMLElementTagNameMap>(
      selector: string,
      options?: Partial<Loggable & Timeoutable & Withinable & Shadow>
    ): Chainable<JQuery<HTMLElementTagNameMap[K]>>;

    /**
     * Yields elements with a data-test attribute that match the selector inside a parent element.
     * @example cy.getBySel('basis-info__faellig-at').findBySel('cta')
     */
    findBySel<K extends keyof HTMLElementTagNameMap>(
      selector: string,
      options?: Partial<Loggable & Timeoutable & Withinable & Shadow>
    ): Chainable<JQuery<HTMLElementTagNameMap[K]>>;

    /**
     * Yields elements with a data-test attribute that match the selector and containing the text.
     * @example cy.containsBySel('vorgang__sidebar__cta', 'Fällig zum')
     */
    containsBySel<K extends keyof HTMLElementTagNameMap>(
      selector: string,
      text: string | number | RegExp,
      options?: Partial<Loggable & Timeoutable & Withinable & Shadow>
    ): Chainable<JQuery<HTMLElementTagNameMap[K]>>;

    /**
     * Select an element from a DsSelect input by its index.
     * @example cy.selectFromDsSelect('vorgang__status', 1)
     */
    selectFromDsSelect(selector: string, idx: number): void;

    /**
     * Select an element from a DsSelect input by its label.
     * @example cy.selectFromDsSelect('vorgang__status', 'Erledigt')
     */
    selectFromDsSelectByLabel(selector: string, label: string): void;

    /**
     * Select an element from a DsSelect input by entering
     * a search query and selecting the first element.
     * @example cy.selectFromDsSelect('vorgang__status', 'Erledigt')
     */
    selectFromDsSelectBySearch(selector: string, query: string): void;

    /**
     * Clears a DsSelect input.
     * @example cy.selectFromDsSelect('vorgang__status')
     */
    clearDsSelect(selector: string): void;

    /**
     * Add a new item to a DsMultiselect input.
     * @example cy.addNewItemToDsMultiselect('mail-vorlage-form__bcc', '<EMAIL>')
     */
    addNewItemToDsMultiselect(selector: string, label: string): void;

    /**
     * Select elements from a DsMultiselect input by their labels.
     * @example cy.selectFromDsMultiselectByLabel('mail-vorlage-form__cc', 'Kunde', 'DEMV')
     */
    selectFromDsMultiselectByLabel(selector: string, ...labels: string[]): void;

    /**
     * Set a given date in a DsDatepicker.
     * @example cy.setDateInDsDatepicker('datepicker', new Date())
     */
    setDateInDsDatepicker(selector: string, date: Date): void;
  }
}
