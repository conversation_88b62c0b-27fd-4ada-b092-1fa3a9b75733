import { addDays, addWeeks, format } from 'date-fns';

import { KorrespondenzResource, VorgangResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

describe('Aktionserstellung', () => {
  it('can create aktionen', () => {
    cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}', 2);
    cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}', 5);
    cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}', 5);
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"makler:name":{"type":"text","value":"Tester 1"}}}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"kunde:anrede-1":{"type":"text","value":"Sehr geehrter Herr Mustermann"}, "makler:signatur:duzend":{"type":"text","value":"Mit freundlichem Gruß, Tester 1"}}}', 2);
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"kunde:anrede-1":{"type":"text","value":"Sehr geehrter Herr Mustermann"}, "makler:signatur:duzend":{"type":"text","value":"Mit freundlichem Gruß, Tester 2"}}}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"kunde:anrede-1":{"type":"text","value":"Sehr geehrter Herr Mustermann"}, "makler:signatur:duzend":{"type":"text","value":"Mit freundlichem Gruß, Tester 1"}}}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"kunde:anrede-1":{"type":"text","value":"Sehr geehrter Herr Mustermann"}, "makler:signatur:duzend":{"type":"text","value":"Mit freundlichem Gruß, Tester 2"}}}', 2);

    cy.intercept({
      method: 'GET',
      url: '/api/vorgaenge/*',
    }).as('getVorgaenge');

    let expectedTimelineLength = 1;

    // create vorgang
    cy.fixture<VorgangResource>('vorgaenge/korrespondenz')
      .then((korrespondenz) => {
        korrespondenz.attributes.faelligAt = addDays(new Date(), 2).toISOString();

        cy.request<Document<KorrespondenzResource>>('POST', '/api/vorgaenge', { data: korrespondenz })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });

    // open vorgang
    cy.visit('/');
    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .first()
          .click();
      });

    // Kommentar
    // create kommentar
    cy.contains('button', 'Kommentar verfassen').click();

    cy.getBySel('aktionen__nachricht__content')
      .type('\'No comment\' is a splendid expression. I am using it again and again. - #makler:name');
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'makler:name')
      .click();

    cy.contains('button', 'Kommentieren').click();
    expectedTimelineLength += 1; // entry

    // check kommentar
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength)
          .last()
          .should('contain.text', '\'No comment\' is a splendid expression. I am using it again and again. - Tester 1');
      });

    // Gespraechsnotiz
    // create gespraechsnotiz
    cy.getBySel('aktionen')
      .should('contain.text', 'Gesprächsnotiz')
      .within(() => {
        cy.contains('button', 'Gesprächsnotiz').click();
        cy.getBySel('aktionen__gespraechstyp').within(() => {
          cy.get('div')
            .contains('Telefon')
            .first()
            .click();
        });
        cy.getBySel('aktionen__nachricht__content')
          .type('I often quote myself. It adds spice to my conversation.');
        cy.contains('button', 'Gesprächsnotiz anlegen').click();
        expectedTimelineLength += 1; // entry
      });

    // check gespraechsnotiz
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength)
          .last()
          .should('contain.text', 'Tester 1')
          .should('contain.text', 'Telefon')
          .should('contain.text', 'I often quote myself. It adds spice to my conversation.');
      });
    // Korrespondenz

    // create korrespondenz
    cy.getBySel('aktionen')
      .should('contain.text', 'Nachricht verfassen')
      .within(() => {
        cy.contains('button', 'Nachricht verfassen').click();
        cy.addNewItemToDsMultiselect('aktionen__cc', '<EMAIL>');
        cy.getBySel('aktionen__nachricht__subject')
          .type('I have no special talent. I am only passionately curious.');
        cy.getBySel('aktionen__nachricht__content')
          .type('If you can\'t explain it simply, you don\'t understand it well enough.');
        cy.contains('button', 'Weiter zur Vorschau').click();
      });

    // check preview
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Weiter zur Vorschau')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau');
      })
      .should('contain.text', 'I have no special talent. I am only passionately curious.')
      .should('contain.text', 'If you can\'t explain it simply, you don\'t understand it well enough.')
      .within(() => {
        cy.contains('button', 'Korrespondenz versenden').click();
        expectedTimelineLength += 1; // entry
      });

    // check korrespondenz in timeline
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength)
          .last()
          .should('contain.text', 'Tester 1')
          .should('contain.text', 'If you can\'t explain it simply, you don\'t understand it well enough.');
      });

    // Korrespondenz im Auftrag von
    // create korrespondenz
    cy.getBySel('aktionen')
      .should('contain.text', 'Nachricht verfassen')
      .within(() => {
        cy.contains('button', 'Nachricht verfassen').click();
        cy.addNewItemToDsMultiselect('aktionen__cc', '<EMAIL>');
        cy.getBySel('aktionen__nachricht__subject')
          .type('Never memorize something that you can look up.');
        cy.getBySel('aktionen__nachricht__content')
          .type('A clever person solves a problem. A wise person avoids it.');
        cy.contains('button', 'Weiter zur Vorschau').click();
      });

    cy.getBySel('aktionen')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau')
          .within(() => {
            cy.getBySel('aktionen__navbar__back')
              .first()
              .click();
          })
          .should('contain.text', 'Nachricht verfassen');
        cy.getBySel('aktionen__im-auftrag-von').click();
      });
    cy.selectFromDsSelect('aktionen__im-auftrag-von__select', 0);
    cy.contains('button', 'Weiter zur Vorschau').click();

    // check preview
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Weiter zur Vorschau')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau');
      })
      .should('contain.text', 'Never memorize something that you can look up.')
      .should('contain.text', 'A clever person solves a problem. A wise person avoids it.')
      .within(() => {
        cy.contains('button', 'Korrespondenz versenden').click();
        expectedTimelineLength += 1; // entry
      });

    // check korrespondenz im auftrag von in timeline
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength)
          .last()
          .should('contain.text', 'Tester 1 im Auftrag von Tester 2')
          .should('contain.text', 'A clever person solves a problem. A wise person avoids it.');
      });

    // Erinnerung
    // create erinnerung 1
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Vorschau')
      .should('contain.text', 'Erinnerung versenden')
      .within(() => {
        cy.contains('button', 'Erinnerung versenden').click();
        cy.setDateInDsDatepicker('aktionen__faellig-at', addDays(new Date(), 1));
        cy.addNewItemToDsMultiselect('aktionen__cc', '<EMAIL>');
        cy.contains('button', 'Weiter zur Vorschau').click();
      });

    // check preview
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Weiter zur Vorschau')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau');
      })
      .should('contain.text', '1. Erinnerung: Ablauf Ihrer Versicherung')
      .should('contain.text', 'leider habe ich zu dem unten stehenden Vorgang von dir noch keine Rückmeldung vorliegen. Bitte gib mir doch möglichst kurzfristig eine Rückmeldung')
      .within(() => {
        cy.contains('button', 'Erinnerung versenden').click();
        expectedTimelineLength += 3; // content + status + faelligkeit
      });

    // check timeline length
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength);
      });

    // check erinnerung in timeline
    cy.getBySel('vorgang__timeline__element')
      .eq(-4)
      .nextAll()
      .should('contain.text', 'leider habe ich zu dem unten stehenden Vorgang von dir noch keine Rückmeldung vorliegen. Bitte gib mir doch möglichst kurzfristig eine Rückmeldung')
      .should('contain.text', 'Mit freundlichem Gruß, Tester 1')
      .should('contain.text', 'Tester 1 hat das Fälligkeitsdatum auf morgen geändert')
      .should('contain.text', 'Tester 1 hat den Status zu 1. Erinnerung geändert');

    // just double checking sidebar
    cy.getBySel('vorgang__sidebar__faelligkeit')
      .should('contain.text', 'morgen');

    // Erinnerung im Auftrag von
    // create erinnerung 2 im auftrag von
    cy.getBySel('aktionen')
      .should('not.contain.text', '1. Erinnerung: Ablauf Ihrer Versicherung')
      .should('contain.text', 'Erinnerung versenden')
      .within(() => {
        cy.contains('button', 'Erinnerung versenden').click();
        cy.getBySel('aktionen__faellig-at__switch')
          .click();
        cy.addNewItemToDsMultiselect('aktionen__cc', '<EMAIL>');
        cy.contains('button', 'Weiter zur Vorschau').click();
      });

    // back to edit and change on behalf of
    cy.getBySel('aktionen')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau')
          .within(() => {
            cy.getBySel('aktionen__navbar__back')
              .first()
              .click();
          })
          .should('contain.text', 'Erinnerung versenden');
        cy.getBySel('aktionen__faellig-at__switch')
          .should('be.visible')
          .click();
        cy.getBySel('aktionen__faellig-at__in-zwei-wochen')
          .should('be.visible')
          .click();
        cy.getBySel('aktionen__im-auftrag-von').click();
      });
    cy.selectFromDsSelect('aktionen__im-auftrag-von__select', 0);
    cy.contains('button', 'Weiter zur Vorschau').click();

    // check preview
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Weiter zur Vorschau')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau');
      })
      .should('contain.text', '2. Erinnerung: Ablauf Ihrer Versicherung')
      .should('contain.text', 'leider muss ich an den unten stehenden Vorgang noch einmal erinnern.')
      .should('contain.text', 'Mit freundlichem Gruß, Tester 2')
      .within(() => {
        cy.contains('button', 'Erinnerung versenden').click();
        expectedTimelineLength += 3; // content + status + faelligkeit
      });

    // check timeline length
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength);
      });

    // check erinnerung im auftrag von in timeline
    cy.getBySel('vorgang__timeline__element')
      .eq(-4)
      .nextAll()
      .should('contain.text', 'Tester 1 im Auftrag von Tester 2')
      .should('contain.text', 'leider muss ich an den unten stehenden Vorgang noch einmal erinnern.')
      .should('contain.text', 'Mit freundlichem Gruß, Tester 2')
      .should('contain.text', `Tester 1 hat das Fälligkeitsdatum auf ${format(addWeeks(new Date(), 2), 'dd.MM.yyyy')} geändert`)
      .should('contain.text', 'Tester 1 hat den Status zu 2. Erinnerung geändert');

    // Mahnung
    // create mahnung 1
    cy.getBySel('aktionen')
      .should('not.contain.text', '2. Erinnerung: Ablauf Ihrer Versicherung')
      .within(() => {
        // check if aktion erinnerung is disabled after the second one was sent
        cy.contains('button', 'Erinnerung versenden').should('be.disabled');

        cy.contains('button', 'Mahnung versenden').click();
        cy.getBySel('aktionen__faellig-at__switch').click();
        cy.addNewItemToDsMultiselect('aktionen__cc', '<EMAIL>');
        cy.contains('button', 'Weiter zur Vorschau').click();
      });

    // check preview
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Weiter zur Vorschau')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau');
      })
      .should('contain.text', '1. Mahnung: Ablauf Ihrer Versicherung')
      .should('contain.text', 'leider liegt mir trotz zwei Erinnerungen noch immer keine Rückmeldung von dir vor.')
      .within(() => {
        cy.contains('button', 'Mahnung versenden').click();
        expectedTimelineLength += 2; // content + status
      });

    // check mahnung in timeline
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength)
          .eq(-3)
          .nextAll()
          .should('contain.text', 'leider liegt mir trotz zwei Erinnerungen noch immer keine Rückmeldung von dir vor.')
          .should('contain.text', 'Mit freundlichem Gruß, Tester 1')
          .should('contain.text', 'Tester 1 hat den Status zu 1. Mahnung geändert');
      });

    // Mahnung im Auftrag von
    // create mahnung 2 in auftrag von
    cy.getBySel('aktionen')
      .should('not.contain.text', '1. Mahnung: Ablauf Ihrer Versicherung')
      .should('contain.text', 'Mahnung versenden')
      .within(() => {
        cy.contains('button', 'Mahnung versenden').click();
        cy.addNewItemToDsMultiselect('aktionen__cc', '<EMAIL>');
        cy.contains('button', 'Weiter zur Vorschau').click();
      });

    // back to edit and change on behalf of
    cy.getBySel('aktionen')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau')
          .within(() => {
            cy.getBySel('aktionen__navbar__back')
              .first()
              .click();
          })
          .should('contain.text', 'Mahnung versenden');
        cy.getBySel('aktionen__im-auftrag-von').click();
      });
    cy.selectFromDsSelect('aktionen__im-auftrag-von__select', 0);
    cy.contains('button', 'Weiter zur Vorschau').click();

    // check preview
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Weiter zur Vorschau')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau');
      })
      .should('contain.text', '2. Mahnung: Ablauf Ihrer Versicherung')
      .should('contain.text', 'an den unten stehenden Vorgang habe ich nun insgesamt zweimal erinnert und einmal gemahnt.')
      .should('contain.text', 'Mit freundlichem Gruß, Tester 2')
      .within(() => {
        cy.contains('button', 'Mahnung versenden').click();
        expectedTimelineLength += 3; // content + status + faelligkeit
      });

    // check mahnung im auftrag von in timeline
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength)
          .eq(-4)
          .nextAll()
          .should('contain.text', 'Tester 1 im Auftrag von Tester 2')
          .should('contain.text', 'an den unten stehenden Vorgang habe ich nun insgesamt zweimal erinnert und einmal gemahnt.')
          .should('contain.text', 'Mit freundlichem Gruß, Tester 2')
          .should('contain.text', `Tester 1 hat das Fälligkeitsdatum auf ${format(addWeeks(new Date(), 1), 'dd.MM.yyyy')} geändert`)
          .should('contain.text', 'Tester 1 hat den Status zu 2. Mahnung geändert');
      });

    // Folgevorgang (Aufgabe)
    cy.getBySel('aktionen')
      .should('not.contain.text', '2. Mahnung: Ablauf Ihrer Versicherung')
      .within(() => {
        // check if aktion mahnung is disabled after the second one was sent
        cy.contains('button', 'Erinnerung versenden').should('be.disabled');

        cy.contains('button', 'Folgevorgang erstellen').click();
      });

    // fill vorgang anlegen modal
    cy.getBySel('vorgang-anlegen__modal')
      .should('contain.text', 'Folgevorgang anlegen')
      .should('contain.text', 'Folgevorgang zu: Ablauf (M-K)')
      .within(() => {
        cy.getBySel('index__radio-button__kommentare').click();
        cy.getBySel('aufgabe__form-basis-info__input__titel').type('Logic will get you from A to Z');
        cy.getBySel('aufgabe__nachricht__content').type('Imagination will get you everywhere.');
        cy.contains('button', 'Vorgang anlegen').click();
        expectedTimelineLength += 1; // entry
      });

    // check vorgangsliste
    cy.wait('@getVorgaenge')
      .its('response.statusCode')
      .should('equal', 200);

    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 2)
          .last()
          .should('contain.text', 'Logic will get you from A to Z');
      });

    // check folgevorgang, timeline and router link
    cy.getBySel('vorgang__titel')
      .should('contain.text', 'Logic will get you from A to Z');

    cy.getBySel('vorgang__timeline__element')
      .should('have.length', 1)
      .should('contain.text', 'Tester 1')
      .should('contain.text', 'Imagination will get you everywhere.');

    cy.getBySel('related_vorgang__router-link')
      .should('contain.text', 'Ablauf (M-K)')
      .click();

    // check the vorgaenger timeline
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength);

        cy.getBySel('vorgang__timeline__element')
          .last()
          .should('contain.text', 'Tester 1 hat den Folgevorgang Logic will get you from A to Z');
      });

    // check sidebar

    cy.getBySel('vorgang__sidebar__related-vorgaenge')
      .within(() => {
        cy.getBySel('vorgang__sidebar__related-vorgaenge__item')
          .should('have.length', 1)
          .should('contain.text', 'Logic will get you from A to Z');
      });

    // Nachricht
    // create nachricht
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Vorschau')
      .should('contain.text', 'Nachricht verfassen')
      .within(() => {
        cy.contains('button', 'Nachricht verfassen').click();
        cy.addNewItemToDsMultiselect('aktionen__cc', '<EMAIL>');
        cy.getBySel('aktionen__nachricht__subject')
          .type('Testnachricht 123 Betreff')
          .type('{enter}');
        cy.getBySel('aktionen__nachricht__content')
          .type('Testnachricht 123 Text')
          .type('{enter}');
        cy.contains('button', 'Weiter zur Vorschau').click();
      });

    // check preview
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Weiter zur Vorschau')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau');
      })
      .should('contain.text', 'Testnachricht 123 Betreff')
      .should('contain.text', 'Testnachricht 123 Text')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .next()
          .children()
          .first()
          .find('button').first().should('contain.text', 'Mailverlauf einblenden');
        cy.get('button').contains('Korrespondenz versenden').first().click();
        expectedTimelineLength += 1; // entry
      });

    // check nachricht in timeline
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength)
          .eq(expectedTimelineLength - 1)
          .should('contain.text', 'Testnachricht 123 Text');
      });

    // Nachricht im Auftrag von
    // create nachricht im auftrag von
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Vorschau')
      .should('contain.text', 'Nachricht verfassen')
      .within(() => {
        cy.get('button').contains('Nachricht verfassen').first().click();
        cy.addNewItemToDsMultiselect('aktionen__cc', '<EMAIL>');
        cy.getBySel('aktionen__nachricht__subject')
          .type('Testnachricht 123 Betreff')
          .type('{enter}');
        cy.getBySel('aktionen__nachricht__content')
          .type('Testnachricht 123 Text')
          .type('{enter}');
        cy.get('button').contains('Weiter zur Vorschau').first().click();
      });

    // back to edit and change on behalf of
    cy.getBySel('aktionen')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau')
          .within(() => {
            cy.getBySel('aktionen__navbar__back')
              .first()
              .click();
          })
          .should('contain.text', 'Nachricht verfassen');
        cy.getBySel('aktionen__im-auftrag-von').click();
      });
    cy.selectFromDsSelect('aktionen__im-auftrag-von__select', 0);
    cy.get('button').contains('Weiter zur Vorschau').first().click();

    // check preview
    cy.getBySel('aktionen')
      .should('not.contain.text', 'Weiter zur Vorschau')
      .within(() => {
        cy.getBySel('aktionen__navbar')
          .should('contain.text', 'Vorschau');
      })
      .should('contain.text', 'Testnachricht 123 Betreff')
      .should('contain.text', 'Testnachricht 123 Text')
      .within(() => {
        cy.get('button').contains('Korrespondenz versenden').first().click();
        expectedTimelineLength += 1; // entry
      });

    // check nachricht im auftrag von in timeline
    cy.getBySel('vorgang__timeline')
      .within(() => {
        cy.getBySel('vorgang__timeline__element')
          .should('have.length', expectedTimelineLength)
          .eq(expectedTimelineLength - 1)
          .should('contain.text', 'Tester 1 im Auftrag von Tester 2')
          .should('contain.text', 'Testnachricht 123 Text');
      });
  });
});
