import addDays from 'date-fns/addDays';

import { KorrespondenzResource, VorgangResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

type SignedStorageUrlResponse = {
  path: string,
  url: string,
  headers: Record<string, string>,
};

describe('Dateiupload', () => {
  // todo: we seem to have a problem with the minio image in our github actions, unskip when fixed
  it.skip('can upload a file', () => {
    cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}');

    cy.fixture<VorgangResource>('vorgaenge/korrespondenz')
      .then((korrespondenz) => {
        korrespondenz.attributes.faelligAt = addDays(new Date(), 2).toISOString();

        cy.request<Document<KorrespondenzResource>>('POST', '/api/vorgaenge', { data: korrespondenz })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });

    cy.request<SignedStorageUrlResponse>({
      method: 'POST',
      url: '/api/signed-storage-url',
      headers: {
        'Content-Type': 'application/json',
      },
    }).then(({ body }) => {
      const { headers } = body;
      if ('Host' in headers) {
        delete headers.Host;
      }

      cy.fixture('upload/test_document.pdf', 'binary').then((document: string) => {
        const blob = Cypress.Blob.binaryStringToBlob(document, 'application/pdf');

        cy.request({
          method: 'PUT',
          url: body.url,
          headers: headers,
          body: blob,
        });
      });

      cy.request({
        method: 'POST',
        url: '/api/files',
        body: {
          data: {
            type: 'files',
            attributes: {
              mimetype: 'application/pdf',
              name: 'test_document.pdf',
              size: 24373,
              tempPath: body.path,
            },
            relationships: {
              owner: {
                data: {
                  id: '1',
                  type: 'korrespondenzen',
                },
              },
            },
          },
        },
      }).then((response) => {
        expect(response.status).to.eq(201);
        expect(response).to.have.property('body');
      });
    });
  });
});
