import { addWeeks } from 'date-fns';
import addMonths from 'date-fns/addMonths';
import format from 'date-fns/format';

import { VorgangResource, VorlageResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

describe('Vorgangerstellung: E-Mail-Korrespondenz', () => {
  it('creates an email vorgang', () => {
    cy.mockPwResponse('get', '/stammdaten/api/ansprechpartner/getAnsprechpartner', '{"status":"success","data":[{"id":"20","sex_descriptor":"Frau","firstname":"<PERSON>","lastname":"Farwick (Bankprodukte)","email":"<EMAIL>","fax":"040 ********","phone_business":"040 ********","phone_mobile":"","homepage":"http:\\/\\/","address":"Holzdamm 53 - 20099 Hamburg","department":""}]}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"datum":{"type":"text","value":"10.12.1815"}}}', 2);
    cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}');

    cy.fixture<VorgangResource>('vorlagen/mail')
      .then((vorlage) => {
        cy.request<Document<VorlageResource>>('POST', '/api/vorlagen', { data: vorlage })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });

    cy.get('button').contains('Vorgang anlegen').first().click();

    /* check mandatory fields */
    cy.intercept({
      method: 'POST',
      url: '/api/vorgaenge',
    }).as('vorgangApiCall');

    cy.getBySel('vorgang-anlegen__modal').find('button').contains('Zur Vorschau').first().click();

    cy.wait('@vorgangApiCall').then((interception) => {
      assert.equal(interception.response?.statusCode, 422);
    });

    cy.getBySel('basis-info__vorgangstyp__select').contains('Vorgangstyp muss ausgefüllt werden.');
    cy.getBySel('empfaenger__select').contains('Empfänger muss ausgefüllt werden.');
    cy.getBySel('vorgang-anlegen__nachricht__betreff').contains('Betreff muss ausgefüllt werden.');
    cy.getBySel('vorgang-anlegen__nachricht__inhalt').contains('Inhalt muss ausgefüllt werden.');

    //* basis informationen */
    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Allgemeine Anfrage an Gesellschaft');

    // VorgangTitel
    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .clear()
      .type('Hier steht ein Titel');

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Ablauf (M-K)');

    cy.contains('Sie haben Änderungen an Titel, Betreff und/oder Inhalt durchgeführt und sind im Begriff eine Vorlage zu laden. Ihre Änderungen gehen damit unwiderruflich verloren.').should('exist');

    cy.get('button').contains('Nein, Änderungen behalten').click();

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .should('have.value', 'Hier steht ein Titel');

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .clear()
      .type('Ich mag Züge');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .type('mayonnaise is a instrument ');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .type('no, mayonnaise is not a instrument');

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Adresswechsel');

    cy.contains('Sie haben Änderungen an Titel, Betreff und/oder Inhalt durchgeführt und sind im Begriff eine Vorlage zu laden. Ihre Änderungen gehen damit unwiderruflich verloren.').should('exist');

    cy.get('button').contains('Nein, Änderungen behalten').click();

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .should('have.value', 'Ich mag Züge');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .contains('mayonnaise is a instrument ')
      .should('exist');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .contains('no, mayonnaise is not a instrument')
      .should('exist');

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Ablauf (M-K)');

    cy.get('button').contains('Ja, Vorlage laden').click();

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .should('have.value', 'Ablauf (M-K)');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .contains('Betreff, Siezend')
      .should('exist');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .contains('Nachricht, Siezend')
      .should('exist');
    cy.selectFromDsSelectBySearch('basis-info__vorgangstyp__select', 'Adresswechsel');

    // Basis Informationen

    cy.getBySel('basis-info__faellig-at')
      .find('input')
      .should('have.value', format(addWeeks(new Date(), 2), 'dd.MM.yyyy'));
    cy.getBySel('basis-info__faellig-at__in-zwei-wochen')
      .should('have.attr', 'aria-checked', 'true');

    cy.getBySel('basis-info__faellig-at__in-einer-woche').click();
    cy.getBySel('basis-info__faellig-at')
      .find('input')
      .should('have.value', format(addWeeks(new Date(), 1), 'dd.MM.yyyy'));

    cy.getBySel('basis-info__faellig-at')
      .clear()
      .type(format(addMonths(new Date(), 2), 'dd.MM.yyyy'))
      .type('{enter}');
    cy.getBySel('basis-info__faellig-at__eigenes-datum')
      .should('have.attr', 'aria-checked', 'true');

    cy.selectFromDsSelectBySearch('basis-info__kunde__select', 'Maria Musterfrau');

    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'swimming pool');

    cy.getBySel('basis-info__vertriebsweg-art__pool').click();

    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'Accent GmbH & Co. KG');

    cy.getBySel('basis-info__vertriebsweg__select')
      .should('contain', 'Fonds Finanz Maklerservice GmbH');

    cy.getBySel('basis-info__vertraege__select')
      .find('input')
      .clear();

    cy.selectFromDsSelectBySearch('basis-info__vorgangstyp__select', 'Antrag einreichen');

    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'Bank Produkt Herr UND Frau Musterfamilie');

    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'Generali Deutschland Lebensversicherung AG (ex AachenMünchener) ');

    cy.getBySel('basis-info__vertriebsweg__select')
      .should('contain', 'Generali Deutschland Lebensversicherung AG (ex AachenMünchener) ');

    cy.getBySel('basis-info__vertraege__select')
      .find('input')
      .clear();

    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', '********* - Gemeinsames KFZ MUSTER');

    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'HDI Sachversicherungen AG');

    cy.getBySel('basis-info__vertriebsweg__select')
      .should('contain', 'Accent GmbH & Co. KG');

    cy.getBySel('basis-info__vertraege__select')
      .find('input')
      .clear();

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Allgemeine Korrespondenz');

    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'Bank Produkt Herr UND Frau Musterfamilie');

    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'Generali Deutschland Lebensversicherung AG (ex AachenMünchener) ');

    cy.getBySel('basis-info__vertriebsweg__select')
      .should('contain', 'Generali Deutschland Lebensversicherung AG (ex AachenMünchener) ');

    cy.getBySel('basis-info__vertraege__select')
      .find('input')
      .clear();

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Ablehnung Kündigung durch VU (M-K)');

    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'Bank Produkt Herr UND Frau Musterfamilie');

    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'Generali Deutschland Lebensversicherung AG (ex AachenMünchener) ');

    cy.getBySel('basis-info__vertraege__select')
      .find('input')
      .clear();

    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', '********* - Gemeinsames KFZ MUSTER');

    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'HDI Sachversicherungen AG');

    cy.selectFromDsSelectBySearch('basis-info__vorgangstyp__select', 'Antrag einreichen');

    cy.selectFromDsSelect('basis-info__gesellschaft__select', 0);
    cy.selectFromDsSelect('basis-info__sparte__select', 0);

    // empfaenger
    cy.addNewItemToDsMultiselect('step1__empfaenger__form', '<EMAIL>');

    // bcc/cc
    cy.getBySel('empfaenger__cc-bcc__switch').click();
    cy.addNewItemToDsMultiselect('step1__cc__form', '<EMAIL>');
    cy.addNewItemToDsMultiselect('step1__bcc__form', '<EMAIL>');

    // content
    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .type('This is your typical e2e test');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .type('Santa mozzarella, today is the #');
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag

    // zur vorschau
    cy.get('button').contains('Zur Vorschau').first().click();

    cy.wait('@vorgangApiCall').then((interception) => {
      assert.equal(interception.response?.statusCode, 201);
    });

    // vorschau
    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('This is your typical e2e test').should('exist');
    cy.contains('Santa mozzarella, today is the 10.12.1815').should('exist');

    // vorgang editieren
    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Zurück')
      .first()
      .click();

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .clear();

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .clear();

    cy.intercept({
      method: 'PUT',
      url: '/api/vorgaenge/1',
    }).as('vorgangApiCallUpdate');

    cy.get('button').contains('Zur Vorschau').first().click();

    cy.wait('@vorgangApiCallUpdate').then((interception) => {
      assert.equal(interception.response?.statusCode, 422);
    });

    cy.getBySel('vorgang-anlegen__nachricht__betreff').contains('Betreff muss ausgefüllt werden.');
    cy.getBySel('vorgang-anlegen__nachricht__inhalt').contains('Inhalt muss ausgefüllt werden.');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .type('This is your typical e2e test');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .type('Santa mozzarella, today is the #');
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag

    cy.get('button').contains('Zur Vorschau').first().click();

    cy.wait('@vorgangApiCallUpdate').then((interception) => {
      assert.equal(interception.response?.statusCode, 200);
    });

    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('This is your typical e2e test').should('exist');
    cy.contains('Santa mozzarella, today is the 10.12.1815').should('exist');

    // vorgang anlegen
    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();

    // test Courtage erfassen popup
    cy.contains('Antrag einreichen');
    cy.get('button').contains('Jetzt nicht').click();

    // test created vorgang
    cy.getBySel('vorgang__titel')
      .should('exist')
      .should('contain.text', 'Adresswechsel');

    cy.getBySel('timeline__list-item__icon')
      .first()
      .should('have.attr', 'data-icon', 'at');
    cy.getBySel('vorgang__timeline')
      .should('exist')
      .should('contain.text', 'Tester 1')
      .should('contain.text', 'Santa mozzarella, today is the 10.12.1815');

    cy.getBySel('vorgang__sidebar__gesellschaft')
      .should('exist')
      .should('contain.text', '1&1');
    cy.getBySel('vorgang__sidebar__sparte')
      .should('exist')
      .should('contain.text', 'Bankprodukte');
    cy.getBySel('vorgang__sidebar__vertriebsweg')
      .should('exist')
      .should('contain.text', '1&1');

    // Kommunikation über wird bei MK ausgeblendet
    cy.get('button').contains('Vorgang anlegen').first().click();
    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Adresswechsel');
    cy.getBySel('basis-info__vertriebsweg__select')
      .should('exist');
    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .clear()
      .type('Hier steht ein Titel');

    cy.selectFromDsSelectBySearch('basis-info__kunde__select', 'Maria Musterfrau');
    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'swimming pool');
    cy.addNewItemToDsMultiselect('step1__empfaenger__form', '<EMAIL>');
    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .type('Hier steht der Betreff');
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .type('Hier steht der Inhalt');
    cy.get('button').contains('Zur Vorschau').first().click();

    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('Hier steht der Inhalt').should('exist');
    cy.contains('Hier steht der Betreff').should('exist');

    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Zurück')
      .first()
      .click();

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Ablauf (M-K)');
    cy.getBySel('basis-info__vertriebsweg__select')
      .should('not.exist');
    cy.get('button').contains('Ja, Vorlage laden').click();
    cy.get('button').contains('Zur Vorschau').first().click();
    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();

    cy.getBySel('vorgang__titel')
      .should('exist')
      .should('contain.text', 'Ablauf (M-K)');
    cy.getBySel('timeline__list-item__icon')
      .first()
      .should('have.attr', 'data-icon', 'at');
    cy.getBySel('vorgang__timeline')
      .should('exist')
      .should('contain.text', 'Tester 1')
      .should('contain.text', 'Nachricht, Duzend');
    cy.getBySel('vorgang__sidebar__vertriebsweg')
      .should('not.exist');
  });
});
