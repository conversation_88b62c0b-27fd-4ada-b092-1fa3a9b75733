describe('Auftragsvorgangerstellung: E-Mail-Korrespondenz', () => {
  it('creates an email auftragsvorgang', () => {
    cy.mockPwResponse('get', '/stammdaten/api/ansprechpartner/getAnsprechpartner', '{"status":"success","data":[{"id":"20","sex_descriptor":"<PERSON><PERSON>","firstname":"<PERSON>","lastname":"Farwick (Bankprodukte)","email":"<EMAIL>","fax":"040 ********","phone_business":"040 ********","phone_mobile":"","homepage":"http:\\/\\/","address":"Holzdamm 53 - 20099 Hamburg","department":""}]}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"makler:name":{"type":"text","value":"Tester 1"}}}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"makler:name":{"type":"text","value":"Tester 3"}}}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"makler:name":{"type":"text","value":"Tester 2"}}}');
    cy.mockPwResponse('post', '/api/mailer/forUser/2/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}');

    cy.intercept('GET', '/api/vorlagen?include=vorlage&filter[vorlage_type]=vorlagenMail&filter[vorlage.vorgangTypId]=89&sort=-updatedAt', '{"data":[{"type":"vorlagen","id":"1041","attributes":{"isEditable":true,"name":"Ablauf Tester 1","type":"Eigene","description":"Kurzbeschreibung Tester 1 Vorlage","erstellerId":1,"usageByOwnerOnly":true,"createdAt":"2022-11-02T10:11:44+01:00","updatedAt":"2022-11-07T10:29:49+01:00","deletedAt":null},"links":{"self":"http://vorgaenge.testing/api/vorlagen/1041"},"relationships":{"vorlage":{"data":{"type":"vorlagenMail","id":"97"}}}}],"links":{"first":null,"last":null,"prev":null,"next":null},"meta":{"path":"http://vorgaenge.testing/api/vorlagen","per_page":25,"total":1},"included":[{"type":"vorlagenMail","id":"97","attributes":{"vorgangTypId":"89","empfaengerTypes":["kunde"],"bcc":[],"cc":[],"attachments":[],"formalSubject":{"type":"oneLineDocument","content":[{"type":"paragraph","content":[{"text":"Ablauf","type":"text"}]}]},"formalContent":{"type":"doc","content":[{"type":"paragraph","attrs":{"textAlign":"left"},"content":[{"text":"Diese Vorlage sieht nur Tester 1","type":"text","marks":[{"type":"textStyle","attrs":{"color":null,"fontSize":null,"fontFamily":"Arial"}}]}]}]}}}]}');
    cy.intercept('GET', '/api/vorlagen?include=vorlage&filter[vorlage_type]=vorlagenMail&filter[vorlage.vorgangTypId]=89&sort=-updatedAt&ersteller=2', '{"data":[{"type":"vorlagen","id":"1042","attributes":{"isEditable":true,"name":"Ablauf Tester 2","type":"Eigene","description":"Kurzbeschreibung Tester 2 Vorlage","erstellerId":2,"usageByOwnerOnly":true,"createdAt":"2022-11-02T10:11:44+01:00","updatedAt":"2022-11-07T10:29:49+01:00","deletedAt":null},"links":{"self":"http://vorgaenge.testing/api/vorlagen/1042"},"relationships":{"vorlage":{"data":{"type":"vorlagenMail","id":"98"}}}}],"links":{"first":null,"last":null,"prev":null,"next":null},"meta":{"path":"http://vorgaenge.testing/api/vorlagen","per_page":25,"total":1},"included":[{"type":"vorlagenMail","id":"98","attributes":{"vorgangTypId":"89","empfaengerTypes":["kunde"],"bcc":[],"cc":[],"attachments":[],"formalSubject":{"type":"oneLineDocument","content":[{"type":"paragraph","content":[{"text":"Ablauf","type":"text"}]}]},"formalContent":{"type":"doc","content":[{"type":"paragraph","attrs":{"textAlign":"left"},"content":[{"text":"Diese Vorlage sieht nur Tester 2","type":"text","marks":[{"type":"textStyle","attrs":{"color":null,"fontSize":null,"fontFamily":"Arial"}}]}]}]}}}]}');

    cy.intercept('/api/*').as('api');

    cy.visit('/');

    cy.get('button').contains('Vorgang anlegen').first().click();

    cy.wait('@api');
    // basis informationen
    // vorgangstyp
    cy.selectFromDsSelect('basis-info__vorgangstyp__select', 0);

    // check if correct Vorlage for Tester 1 is filled in
    cy.getBySel('vorgang-anlegen__nachricht__inhalt')
      .contains('Diese Vorlage sieht nur Tester 1');

    // check if "als Vorlage speichern" is enabled
    cy.getBySel('vorgang-anlegen__save-as-vorlage')
      .should('not.be.disabled');

    // gesellschaft
    cy.selectFromDsSelect('basis-info__gesellschaft__select', 0);

    // sparte
    cy.selectFromDsSelect('basis-info__sparte__select', 0);

    // content as Tester 1
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .clear()
      .type('The one and only #makler:name');
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'makler:name')
      .should('exist')
      .click();

    cy.getBySel('step1__empfaenger__form').type('<EMAIL>').type('{enter}');

    // to preview
    cy.get('button').contains('Zur Vorschau').first().click();

    // check if tag was replaced correctly
    cy.contains('The one and only Tester 1').should('exist');

    // back to edit for further on behalf of testing
    cy.get('button').contains('Zurück').first().click();

    //set tester 3 and preview - to later make sure a temporarily selected user is not in bearbeiter/Beobachter
    cy.getBySel('vorgang-anlegen__im-auftrag-von').click();
    cy.selectFromDsSelectByLabel('vorgang-anlegen__im-auftrag-von__select', 'Tester 3');
    cy.contains('Nein, Änderungen behalten')
      .should('have.length', 1)
      .first()
      .click();

    cy.getBySel('step1__empfaenger__form').type('<EMAIL>').type('{enter}');
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .clear()
      .type('I truly am #makler:name');
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'makler:name')
      .should('exist')
      .click();

    cy.get('button').contains('Zur Vorschau').first().click();
    // check if tag was replaced correctly
    cy.contains('I truly am Tester 3').should('exist');
    // back to edit for further on behalf of testing
    cy.get('button').contains('Zurück').first().click();
    //select other user to complete test

    // auftragsvorgang
    cy.getBySel('vorgang-anlegen__im-auftrag-von').click();
    cy.selectFromDsSelectByLabel('vorgang-anlegen__im-auftrag-von__select', 'Tester 2');
    cy.contains('Ja, Vorlage laden')
      .should('have.length', 1)
      .first()
      .click();

    // empfaenger
    cy.getBySel('step1__empfaenger__form').type('<EMAIL>').type('{enter}');

    // bcc/cc
    cy.getBySel('empfaenger__cc-bcc__switch').click();
    cy.getBySel('step1__cc__form').type('<EMAIL>').type('{enter}');
    cy.getBySel('step1__bcc__form').type('<EMAIL>').type('{enter}');

    // check if correct Vorlage for Tester 1 is filled in
    cy.getBySel('vorgang-anlegen__nachricht__inhalt')
      .contains('Diese Vorlage sieht nur Tester 2');

    // check if "als Vorlage speichern" is enabled
    cy.getBySel('vorgang-anlegen__save-as-vorlage')
      .should('be.disabled');

    // content
    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .clear()
      .type('This is your typical e2e test');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .clear()
      .type('I truly am #makler:name');
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'makler:name')
      .should('exist')
      .click();
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .contains('[data-name="makler:name"]', 'makler:name')
      .should('exist'); // date tag

    // next
    cy.get('button').contains('Zur Vorschau').first().click().wait('@api');

    // ---> step 2 <----
    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('This is your typical e2e test').should('exist');
    cy.contains('I truly am Tester 2').should('exist');

    //next
    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();
    cy.wait('@api');

    // ----> check if everything is alright in vorgaenge <----

    cy.contains('Ablauf (M-K)').should('exist');
    cy.contains('Tester 1 im Auftrag von Tester 2').should('exist');
    cy.contains('I truly am Tester 2').should('exist');
    cy.get('[data-test="vorgang__sidebar__bearbeiter"]').contains('Tester 1').should('exist');
    cy.get('[data-test="vorgang__sidebar__bearbeiter"]').contains('Tester 2').should('exist');
    cy.get('[data-test="vorgang__sidebar__bearbeiter"]').contains('Tester 3').should('not.exist');
    cy.get('[data-test="vorgang__sidebar__beobachter"]').contains('Tester 1').should('exist');
    cy.get('[data-test="vorgang__sidebar__beobachter"]').contains('Tester 2').should('exist');
    cy.get('[data-test="vorgang__sidebar__beobachter"]').contains('Tester 3').should('not.exist');
  });
});
