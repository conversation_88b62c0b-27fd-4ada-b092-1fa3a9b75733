import { addDays } from 'date-fns';

import { VorgangResource, VorlageResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';
import format from 'date-fns/format';

describe('Kampagne erstellen', () => {
  it('can create kampagne', () => {
    // create vorlage
    cy.fixture<VorgangResource>('vorlagen/kampagneMail')
      .then((vorlage) => {
        cy.request<Document<VorlageResource>>(
          'POST',
          '/api/vorlagen',
          { data: vorlage },
        )
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });

    // create Kampagne
    cy.visit('/kampagnen');

    cy.getBySel('kampagne-uebersicht__entwurf-table').within(() => {
      cy.contains('Klicken Sie oben rechts, um eine neue Kampagne zu erstellen').should('exist');
    });
    cy.get('a').contains('Geplant').first().click();
    cy.getBySel('kampagne-uebersicht__geplant-table').within(() => {
      cy.contains('Planen Sie den Versand einer Kampagne für einen späteren Zeitpunkt, um sie hier zu sehen').should('exist');
    });

    cy.get('a').contains('Versendet').first().click();
    cy.getBySel('kampagne-uebersicht__abgeschlossen-table').within(() => {
      cy.contains('Schließen Sie eine Kampagne ab, um sie hier zu sehen').should('exist');
    });

    cy.getBySel('kampagne__page-title').contains('Kampagnen');
    cy.get('button').contains('Kampagne erstellen').first().click();
    cy.get('button').contains('Ohne Idee fortfahren').first().click();

    // Basis Konfiguration
    cy.getBySel('kampagne-create__stepper__step1').children().first()
      .should('have.attr', 'data-active', 'true');
    cy.getBySel('kampagne__page-title').contains('Neue Kampagne');

    cy.get('button').contains('Weiter').first().click();
    cy.getBySel('kampagne-create__step1__titel')
      .contains('Kampagnentitel muss ausgefüllt werden.');
    cy.getBySel('kampagne-create__step1__titel').type('Uga Buga');
    cy.get('button').contains('Weiter').first().click();

    cy.get('button').contains('Zurück').first().click();
    cy.get('button').contains('Kampagne erstellen').first().click();
    cy.visit('/kampagnen');

    cy.getBySel('kampagne-uebersicht__entwurf-table').within(() => {
      cy.contains('Kein Titel').should('not.exist');
    });

    cy.getBySel('kampagne-uebersicht__edit-button').first().click();
    cy.getBySel('kampagne-create__step1__titel').find('input').clear().type('Test Titel');
    cy.get('body').click();
    cy.reload();
    cy.getBySel('kampagne-create__step1__titel').find('input').clear().type('Uga Buga');
    cy.get('body').click();
    cy.visit('/kampagnen');

    cy.getBySel('kampagne-uebersicht__entwurf-table').within(() => {
      cy.contains('Kein Titel').should('not.exist');
    });
    cy.getBySel('kampagne-uebersicht__edit-button').first().click();
    cy.get('button').contains('Weiter').first().click();

    // Empfaenger
    cy.getBySel('kampagne-create__stepper__step2').children().first()
      .should('have.attr', 'data-active', 'true');

    cy.get('button').contains('Weiter').first().click();
    cy.getBySel('kampagne-create__step2')
      .contains('Empfängerliste muss ausgefüllt werden.');
    cy.getBySel('kampagne__page-title').contains('Uga Buga - Empfänger');
    cy.selectFromDsSelectBySearch(
      'basis-info__kunde__select',
      'Gabriela Bavarai',
    );
    cy.selectFromDsSelectBySearch(
      'basis-info__kunde__select',
      'Max Mustermann',
    );

    cy.get('div[class*="itemRow"]').eq(1)
      .within(() => {
        cy.contains('Gabriela Bavarai').should('exist');
        cy.contains('<EMAIL>').should('exist');
        cy.contains('Adresse vorhanden').should('not.exist');
      });
    cy.get('div[class*="itemRow"]').eq(2)
      .within(() => {
        cy.contains('Max Mustermann').should('exist');
        cy.contains('<EMAIL>').should('exist');
        cy.contains('Adresse fehlt / unvollständig').should('not.exist');
      });

    cy.get('button').contains('Weiter').first().click();

    // Inhalt
    cy.getBySel('kampagne-create__stepper__step3').children().first()
      .should('have.attr', 'data-active', 'true');
    cy.intercept({
      method: 'GET',
      url: 'api/vorlagen?include=vorlage&filter[vorlage_type]=vorlagenKampagneMail&sort=-updatedAt',
    }).as('getVorlagen');

    cy.wait('@getVorlagen').then((interception) => {
      assert.equal(interception.response?.statusCode, 200);
    });
    cy.get('button').contains('Weiter').first().click();

    cy.getBySel('kampagne__page-title').contains('Uga Buga - Inhalt');
    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__formal')
      .contains('Betreff (Siezen)').should('exist');
    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__formal')
      .contains('Betreff (Siezen) muss ausgefüllt werden.');
    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__formal')
      .contains('Der Inhalt (Siezen) darf nicht leer sein.');

    cy.getBySel('kampagne__nachricht__betreff').type('Hello World');
    cy.getBySel('kampagne__nachricht__content').type('Hello World');

    // Inhalt Du-Anrede
    cy.get('a').contains('Du-Anrede').first().click();
    cy.getBySel('kampagne__nachricht__betreff').type('Hello World');
    cy.get('button').contains('Weiter').first().click();

    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__informal')
      .contains('Betreff (Duzen) muss ausgefüllt werden.').should('not.exist');
    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__informal')
      .contains('Der Inhalt (Duzen) darf nicht leer sein.');

    cy.get('[data-test="kampagne__nachricht__betreff"] .ProseMirror')
      .first()
      .clear();
    cy.getBySel('kampagne__nachricht__content').type('Hello World');

    cy.get('button').contains('Weiter').first().click();

    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__informal')
      .contains('Betreff (Duzen) muss ausgefüllt werden.');
    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__informal')
      .contains('not.exist', 'Der Inhalt (Duzen) darf nicht leer sein.')
      .should('not.exist');

    cy.getBySel('kampagne__nachricht__betreff').type('Hello World');
    cy.get('a').contains('Sie-Anrede').first().click();

    cy.selectFromDsSelect('kampagne-create__step3__vorlage-select', 0);
    cy.get('button').contains('Weiter').first().click();

    // Zusammenfassung
    cy.getBySel('kampagne-create__stepper__step4').children().first()
      .should('have.attr', 'data-active', 'true');
    cy.getBySel('kampagne__page-title').contains('Uga Buga - Zusammenfassung');
    cy.getBySel('kampagne-create__step4__absender').contains('Tester 1');
    cy.getBySel('kampagne-create__step4__empfaenger').contains('2');
    cy.getBySel('kampagne-create__step4__formal-betreff')
      .contains('Betreff, Siezend');
    cy.getBySel('kampagne-create__step4__formal-content')
      .contains('Nachricht, Siezend');
    cy.getBySel('kampagne-create__step4__informal-betreff')
      .contains('Betreff, Duzend');
    cy.getBySel('kampagne-create__step4__informal-content')
      .contains('Nachricht, Duzend');

    cy.getBySel('kampagne-create__step4__informal-betreff')
      .find('button').first().click();
    cy.getBySel('kampagne-create__stepper__step3').children().first()
      .should('have.attr', 'data-active', 'true');

    // Duzend Inhalt ändern
    cy.get('[data-test="kampagne__nachricht__betreff"] .ProseMirror')
      .first()
      .clear();
    cy.getBySel('kampagne__nachricht__betreff').type('Neuer Betreff, Duzend');
    cy.get('[data-test="kampagne__nachricht__content"] .ProseMirror')
      .first()
      .clear();
    cy.getBySel('kampagne__nachricht__content').type('Neue Nachricht, Duzend');

    cy.get('button').contains('Weiter').first().click();
    cy.getBySel('kampagne-create__step4__informal-betreff')
      .contains('Neuer Betreff, Duzend');
    cy.getBySel('kampagne-create__step4__informal-content')
      .contains('Neue Nachricht, Duzend');

    // Brief Basis Konfiguration
    cy.getBySel('kampagne-create__step4__absender')
      .find('button').first().click();
    cy.getBySel('kampagne-create__step1__versandart__brief').first().click();
    cy.getBySel('kampagne__page-title').contains('Uga Buga - Basis-Konfiguration');
    cy.get('button').contains('Weiter').first().click();

    // Brief Empfaenger
    cy.get('div[class*="itemRow"]').eq(1)
      .within(() => {
        cy.contains('Gabriela Bavarai').should('exist');
        cy.contains('<EMAIL>').should('not.exist');
        cy.contains('Adresse vorhanden').should('exist');
      });
    cy.get('div[class*="itemRow"]').eq(2)
      .within(() => {
        cy.contains('Max Mustermann').should('exist');
        cy.contains('<EMAIL>').should('not.exist');
        cy.contains('Adresse fehlt / unvollständig').should('exist');
      });
    cy.get('button').contains('Weiter').first().click();

    // Brief Inhalt
    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__formal')
      .contains('Betreff (Siezen)').should('not.exist');

    cy.get('button').contains('Weiter').first().click();

    // Brief Zusammenfassung
    cy.getBySel('kampagne__page-title').contains('Uga Buga - Zusammenfassung');
    cy.getBySel('kampagne-create__step4__absender').contains('Tester 1');
    cy.getBySel('kampagne-create__step4__empfaenger').contains('2');
    cy.getBySel('kampagne-create__step4__formal-betreff').should('not.exist');
    cy.getBySel('kampagne-create__step4__formal-content').contains('Nachricht, Siezend');
    cy.getBySel('kampagne-create__step4__informal-betreff').should('not.exist');
    cy.getBySel('kampagne-create__step4__informal-content').contains('Nachricht, Duzend');

    // Übersicht
    cy.get('button').contains('Zur Übersicht').first().click();
    cy.getBySel('kampagne-uebersicht__entwurf-table').within(() => {
      cy.contains('Uga Buga').should('exist');
      cy.contains('Tester 1').should('exist');
      cy.contains('heute').should('exist');
      cy.contains('2').should('exist');
      cy.contains('Klicken Sie oben rechts, um eine neue Kampagne zu erstellen').should('not.exist');
    });

    cy.getBySel('kampagne-uebersicht__delete-button').first().click();
    cy.get('button').contains('Ja, Kampagne löschen').click();
    cy.getBySel('kampagne-uebersicht__entwurf-table').within(() => {
      cy.contains('Uga Buga').should('not.exist');
      cy.contains('Tester 1').should('not.exist');
      cy.contains('heute').should('not.exist');
      cy.contains('2').should('not.exist');
      cy.contains('Klicken Sie oben rechts, um eine neue Kampagne zu erstellen').should('exist');
    });

    // Create new Kampagne
    cy.get('button').contains('Kampagne erstellen').first().click();
    cy.get('button').contains('Ohne Idee fortfahren').first().click();

    // Basis Konfiguration
    cy.getBySel('kampagne__page-title').contains('Neue Kampagne');
    cy.getBySel('kampagne-create__step1__titel').type('Uga Buga');
    cy.get('button').contains('Weiter').first().click();

    // Empfaenger
    cy.intercept({
      method: 'GET',
      url: '/api/kampagnen/2/empfaengers?fields[kunden]=name,firstName,lastName,email,externalId,informal',
    }).as('kampagneApiCallGetEmpfaenger2');
    cy.wait('@kampagneApiCallGetEmpfaenger2').then((interception) => {
      assert.equal(interception.response?.statusCode, 200);
    });

    cy.getBySel('kampagne__page-title').contains('Uga Buga - Empfänger');
    cy.selectFromDsSelectBySearch(
      'basis-info__kunde__select',
      'Gabriela Bavarai',
    );
    cy.selectFromDsSelectBySearch(
      'basis-info__kunde__select',
      'Max Mustermann',
    );

    cy.get('div[class*="itemRow"]').eq(1)
      .within(() => {
        cy.contains('Gabriela Bavarai').should('exist');
        cy.contains('<EMAIL>').should('exist');
        cy.contains('Adresse vorhanden').should('not.exist');
      });
    cy.get('div[class*="itemRow"]').eq(2)
      .within(() => {
        cy.contains('Max Mustermann').should('exist');
        cy.contains('<EMAIL>').should('exist');
        cy.contains('Adresse fehlt / unvollständig').should('not.exist');
      });

    cy.get('button').contains('Weiter').first().click();

    // Inhalt
    cy.getBySel('kampagne__page-title').contains('Uga Buga - Inhalt');
    cy.getBySel('kampagnen__duzen-und-siezen__nachricht__formal')
      .contains('Betreff (Siezen)').should('exist');

    cy.getBySel('kampagne__nachricht__betreff').type('Betreff, Siezend');
    cy.getBySel('kampagne__nachricht__content').type('Nachricht, Siezend');
    cy.get('a').contains('Du-Anrede').first().click();
    cy.getBySel('kampagne__nachricht__betreff').type('Betreff, Duzend');
    cy.getBySel('kampagne__nachricht__content').type('Nachricht, Duzend');
    cy.get('button').contains('Weiter').first().click();

    // Zusammenfassung
    cy.getBySel('kampagne__page-title').contains('Uga Buga - Zusammenfassung');
    cy.getBySel('kampagne-create__step4__absender').contains('Tester 1');
    cy.getBySel('kampagne-create__step4__empfaenger').contains('2');
    cy.getBySel('kampagne-create__step4__formal-betreff')
      .contains('Betreff, Siezend');
    cy.getBySel('kampagne-create__step4__formal-content')
      .contains('Nachricht, Siezend');
    cy.getBySel('kampagne-create__step4__informal-betreff')
      .contains('Betreff, Duzend');
    cy.getBySel('kampagne-create__step4__informal-content')
      .contains('Nachricht, Duzend');

    cy.get('button').contains('Senden / Versand planen').first().click();

    // Versand Planen
    const date = addDays(new Date(), 1);
    date.setHours(12, 0); // we cannot test for exact time as currents enforces utc and we use local time

    cy.getBySel('kampagne-create__step4__show-time').click();
    cy.get('.el-date-editor.el-date-editor--datetime')
      .find('input')
      .type('{selectall}')
      .type(format(date, 'dd.MM.yyyy HH:mm'));

    cy.get('button').filter((_, el) => el.innerText.trim() === 'Versand planen').click();

    // Übersicht
    cy.getBySel('kampagne-uebersicht__entwurf-table').within(() => {
      cy.contains('Uga Buga').should('not.exist');
      cy.contains('Tester 1').should('not.exist');
      cy.contains('morgen').should('not.exist');
      cy.contains('2').should('not.exist');
      cy.contains('Klicken Sie oben rechts, um eine neue Kampagne zu erstellen').should('exist');
    });

    // Geplante Kampagnen
    cy.get('a').contains('Geplant').first().click();
    cy.getBySel('kampagne-uebersicht__geplant-table').within(() => {
      cy.contains('Uga Buga').should('exist');
      cy.contains('Tester 1').should('exist');
      cy.contains('morgen').should('exist');
      cy.contains('2').should('exist');
      cy.contains('Planen Sie den Versand einer Kampagne für einen späteren Zeitpunkt, um sie hier zu sehen').should('not.exist');
    });

    // Planung abbrechen
    cy.getBySel('kampagne-uebersicht__edit-button').first().click();
    cy.get('button').contains('Ja, Planung abbrechen').click();

    // Absenden
    cy.get('button').contains('Weiter').first().click();
    cy.get('button').contains('Weiter').first().click();
    cy.get('button').contains('Weiter').first().click();
    cy.get('button').contains('Senden / Versand planen').first().click();
    cy.get('button').contains('Kampagne senden').first().click();

    // Einzelseite
    cy.getBySel('kampagne__page-title').contains('Uga Buga');

    cy.getBySel('kampagne-einzelseite__messages-table').within(() => {
      cy.contains('Gabriela Bavarai').should('exist');
      cy.contains('<EMAIL>').should('exist');
      cy.contains('Wird versendet').should('exist');
      cy.contains('Siezend').should('exist');

      cy.contains('Max Mustermann').should('exist');
      cy.contains('<EMAIL>').should('exist');
      cy.contains('Wird versendet').should('exist');
      cy.contains('Siezend').should('exist');
    });

    // Abgeschlossene Kampagnen
    cy.get('button').contains('Zur Übersicht').first().click();
    cy.get('a').contains('Versendet').first().click();

    cy.getBySel('kampagne-uebersicht__abgeschlossen-table').within(() => {
      cy.contains('Uga Buga').should('exist');
      cy.contains('Tester 1').should('exist');
      cy.contains('heute').should('exist');
      cy.contains('Abgeschlossen').should('exist');
      cy.contains('2').should('exist');
      cy.contains('Schließen Sie eine Kampagne ab, um sie hier zu sehen').should('not.exist');
    });
  });
});
