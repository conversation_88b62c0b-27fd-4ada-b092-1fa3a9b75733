import { addMonths } from 'date-fns';

describe('Vorgangerstellung: Aufgabe', () => {
  it('creates an aufgabe', () => {
    cy.mockPwResponse('get', '/stammdaten/api/ansprechpartner/getAnsprechpartner', '{"status":"success","data":[{"id":"20","sex_descriptor":"Fr<PERSON>","firstname":"<PERSON>","lastname":"Farwick (Bankprodukte)","email":"<EMAIL>","fax":"040 ********","phone_business":"040 ********","phone_mobile":"","homepage":"http:\\/\\/","address":"Holzdamm 53 - 20099 Hamburg","department":""}]}');
    cy.mockPwResponse('get', '/api/tags/replace', '{"status":"success","data":{"datum":{"type":"text","value":"10.12.1815"}}}', 2);
    cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}');

    cy.get('button').contains('Vorgang anlegen').first().click();

    // Kommunikation über wird bei Aufgaben ausgeblendet
    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Adresswechsel');
    cy.getBySel('basis-info__vertriebsweg__select')
      .should('exist');
    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .clear()
      .type('Hier steht ein Titel');
    cy.selectFromDsSelectBySearch('basis-info__kunde__select', 'Maria Musterfrau');
    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'swimming pool');
    cy.addNewItemToDsMultiselect('step1__empfaenger__form', '<EMAIL>');
    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .type('Hier steht der Betreff');
    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .type('Hier steht der Inhalt');

    cy.get('button').contains('Zur Vorschau').first().click();

    cy.contains('<EMAIL>').should('exist');
    cy.contains('<EMAIL>').should('exist');
    cy.contains('Hier steht der Inhalt').should('exist');
    cy.contains('Hier steht der Betreff').should('exist');

    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Zurück')
      .first()
      .click();

    cy.get('button').contains('Zurücksetzen').first().click();

    //switch to aufgabe/kommentare
    cy.getBySel('index__radio-button__kommentare').click();

    /* check mandatory fields */
    cy.intercept({
      method: 'POST',
      url: '/api/vorgaenge',
    }).as('vorgangApiCall');

    cy.getBySel('vorgang-anlegen__modal').find('button').contains('Vorgang anlegen').first().click();

    cy.wait('@vorgangApiCall').then((interception) => {
      assert.equal(interception.response?.statusCode, 422);
    });

    cy.getBySel('aufgabe__nachricht__content').contains('Inhalt muss ausgefüllt werden.');

    // basis informationen

    // fill title with a title that is too long
    cy.getBySel('aufgabe__form-basis-info__input__titel')
      .type('Title TOOO LONG - TO BEAUCOUP - I like firsts. Good or bad, they’re always memorable. Title TOOO LONG - TO BEAUCOUP - I like firsts. Good or bad, they’re always memorable. Title TOOO LONG - TO BEAUCOUP - I like firsts. Good or bad, they’re always memorable.');

    cy.setDateInDsDatepicker('basis-info__faellig-at', addMonths(new Date(), 2));
    cy.selectFromDsSelectBySearch('basis-info__kunde__select', 'Gabriela Bavarai');
    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'Frau Bavarais Vertrag');

    // ensure changing vertrag sets gesellschaft and sparte
    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'Accent GmbH & Co. KG');
    cy.getBySel('basis-info__sparte__select')
      .should('contain', 'Förderdarlehen');

    // content
    cy.get('[data-test="aufgabe__nachricht__content"] .ProseMirror')
      .type('Lord Vader, I should have known. Only you could be so bold. #');
    cy.get('[data-tippy-root]')
      .should('have.length', 1)
      .contains('button', 'datum')
      .should('exist')
      .click();
    cy.get('[data-test="aufgabe__nachricht__content"] .ProseMirror')
      .contains('[data-name=datum]', 'datum')
      .should('exist'); // date tag

    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();
    cy.getBySel('vorgang-anlegen__modal')
      .should('contain.text', 'Titel darf maximal');
    cy.getBySel('aufgabe__form-basis-info__input__titel')
      .clear()
      .type('Aufgabentitel');

    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();

    // test created vorgang
    cy.getBySel('vorgang__titel')
      .should('exist')
      .should('contain.text', 'Aufgabentitel');

    cy.getBySel('timeline__list-item__icon')
      .first()
      .should('have.attr', 'data-icon', 'list-check');
    cy.getBySel('vorgang__timeline')
      .should('exist')
      .should('contain.text', 'Tester 1')
      .should('contain.text', 'Lord Vader, I should have known. Only you could be so bold. 10.12.1815');

    cy.getBySel('vorgang__sidebar__kunde')
      .should('exist')
      .should('contain.text', 'Gabriela Bavarai');
    cy.getBySel('vorgang__sidebar__gesellschaft')
      .should('exist')
      .should('contain.text', 'Accent GmbH & Co. KG');
    cy.getBySel('vorgang__sidebar__vertrag')
      .should('exist')
      .should('contain.text', 'Frau Bavarais Vertrag');
    cy.getBySel('vorgang__sidebar__sparte')
      .should('exist')
      .should('contain.text', 'Förderdarlehen');
    cy.getBySel('vorgang__sidebar__vertriebsweg')
      .should('not.exist');

    cy.get('button').contains('Vorgang anlegen').first().click();
    cy.getBySel('index__radio-button__kommentare').click();

    cy.getBySel('aufgabe__form-basis-info__input__titel')
      .type('Japan is beautiful');

    cy.getBySel('basis-info__ohne__faelligkeit')
      .click();

    cy.get('[data-test="aufgabe__nachricht__content"] .ProseMirror')
      .type('i like sushi');

    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();

    cy.getBySel('vorgang__titel')
      .should('exist')
      .should('contain.text', 'Japan is beautiful');

    cy.getBySel('vorgang__timeline')
      .should('exist')
      .should('contain.text', 'Tester 1')
      .should('contain.text', 'i like sushi');

    cy.getBySel('vorgang-status__badge')
      .contains('Offen');
  });
});
