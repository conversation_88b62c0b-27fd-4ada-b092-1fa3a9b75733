import { VorgangResource, VorlageResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

describe('Settings', () => {
  it('Changes User and Companysettings', () => {
    cy.fixture<VorgangResource>('vorlagen/mail')
      .then((vorlage) => {
        cy.request<Document<VorlageResource>>('POST', '/api/vorlagen', { data: vorlage })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });

    // Vorgangstitel Vorlagennamen
    cy.visit('/einstellungen');

    cy.contains('#skeleton').should('not.exist');

    cy.getBySel('user-settings__vorgangstitel-source__vorlage').click();
    cy.getBySel('firmen-settings__firmenkuerzel')
      .clear()
      .type('AFA');

    cy.intercept({
      method: 'PUT',
      url: '/api/firmenSettings/1',
    }).as('putFirmensettings');

    cy.wait('@putFirmensettings').then((interception) => {
      assert.equal(interception.response?.statusCode, 200);
    });

    cy.get('button').contains('Vorgang anlegen').first().click();

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Ablauf (M-K)');

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .should('have.value', 'Mailvorlage');

    cy.addNewItemToDsMultiselect('step1__empfaenger__form', '<EMAIL>');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .clear()
      .type('who let the dogs out?');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .clear()
      .type('Wuff wuff wuff wuff wuff');

    cy.get('button').contains('Zur Vorschau').first().click();

    cy.contains('who let the dogs out? (AFA-').should('exist');

    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();

    // Vorgangstitel Vorgangstyp
    cy.visit('/einstellungen');

    cy.getBySel('user-settings__vorgangstitel-source__vorgangstyp').click();

    cy.getBySel('firmen-settings__firmenkuerzel')
      .find('input')
      .clear()
      .type('RED');

    cy.wait('@putFirmensettings').then((interception) => {
      assert.equal(interception.response?.statusCode, 200);
    });

    cy.get('button').contains('Vorgang anlegen').first().click();

    cy.selectFromDsSelectByLabel('basis-info__vorgangstyp__select', 'Ablauf (M-K)');

    cy.getBySel('basis-info__vorgangstitel')
      .find('input')
      .should('have.value', 'Ablauf (M-K)');

    cy.addNewItemToDsMultiselect('step1__empfaenger__form', '<EMAIL>');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .clear()
      .type('I like turtles');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .clear()
      .type('Nett hier, aber waren Sie schon mal in Baden Württemberg??');

    cy.get('button').contains('Zur Vorschau').first().click();

    cy.contains('I like turtles (RED-').should('exist');

    cy.getBySel('vorgang-anlegen__modal')
      .find('button')
      .contains('Vorgang anlegen')
      .first()
      .click();
  });
});
