describe('Login + Rechte-Check', () => {
  it('CAN login with right "Vorgaenge" and CANT login without it', () => {
    cy.logout();

    cy.fixture<string>('token/login_token')
      .then((token) => (
        cy.request({
          url: `login/pw/callback?token=${token}`,
          followRedirect: false,
          failOnStatusCode: false,
        })
      ))
      .then((resp) => {
        expect(resp.status).to.eq(302);

        // remove trailing forward slashes
        const redirectedToUrl = resp.redirectedToUrl?.replace(/\/+$/, '');

        expect(redirectedToUrl).to.eq(Cypress.config().baseUrl);
      });

    cy.logout();

    cy.fixture<string>('token/login_token_no_service_id')
      .then((token) => (
        cy.request({
          url: `login/pw/callback?token=${token}`,
          followRedirect: false,
          failOnStatusCode: false,
        })
      ))
      .then((resp) => {
        expect(resp.status).to.eq(400);
      });
  });
});
