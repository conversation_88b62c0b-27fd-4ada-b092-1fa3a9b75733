describe('Vorgangerstellung: Initialize Store', () => {
  it('initializes vorgangAnlegenStore', () => {
    cy.mockPwResponse('get', 'home/kundenakte/3/dokumente', JSON.stringify({
      status: 'success',
      data: {
        documents: [
          {
            id: 1,
            name: 'ABC (+contract, +company)',
            extension: 'pdf',
            type: 50,
            contract: 13485,
            company: 156,
            product: null,
            createdAt: '2022-01-09',
          },
          {
            id: 2,
            name: 'DEF (-contract, -company)',
            extension: 'pdf',
            type: 50,
            contract: null,
            company: null,
            product: null,
            createdAt: '2022-01-10',
          },
        ],
        types: [{
          id: 50,
          name: 'Sonstiges',
        }],
      },
    }));

    cy.mockPwResponse('get', 'home/kundenakte/3/dokument/1/meta', JSON.stringify({
      status: 'success',
      data: {
        id: 1,
        name: 'ABC (+contract, +company)',
        extension: 'pdf',
        type: 50,
        contract: 13485,
        company: 156,
        product: null,
        bedarfId: null,
        createdAt: '2022-01-09',
        createType: 1,
        typeId: 50,
        url: 'http://s3.demv.internal/demv-dev-pw-client-files/3/ABC_(+contract,_+company).pdf',
      },
    }));
    cy.mockPwResponse('get', 'home/kundenakte/3/dokument/2/meta', JSON.stringify({
      status: 'success',
      data: {
        id: 2,
        name: 'DEF (-contract, -company)',
        extension: 'pdf',
        type: 50,
        contract: null,
        company: null,
        product: null,
        bedarfId: null,
        createdAt: '2022-01-09',
        createType: 1,
        typeId: 50,
        url: 'http://s3.demv.internal/demv-dev-pw-client-files/3/DEF(-contract,_-company).pdf',
      },
    }));

    // call with external ids
    cy.visit('/api/external/redirect/vorgang/anlegen?vorgangAnlegen[kunde]=3&vorgangAnlegen[sparte]=479&vorgangAnlegen[vertraege]=209901&vorgangAnlegen[vorgangstyp]=58&vorgangAnlegen[kundendokumente]=1,2')
      .then(() => {
        // check mapping
        cy.location('search')
          // these should be mapped
          .should('contain', 'vorgangAnlegen%5Bkunde%5D=3')
          .should('contain', 'vorgangAnlegen%5Bsparte%5D=479')
          .should('contain', 'vorgangAnlegen%5Bvertraege%5D=4')
          // these cannot be mapped
          .should('contain', 'vorgangAnlegen%5Bvorgangstyp%5D=58')
          .should('contain', 'vorgangAnlegen%5Bkundendokumente%5D=1%2C2');
      });

    // check if fields are filled
    cy.getBySel('basis-info__vorgangstyp__select')
      .should('contain', 'Allgemeine Korrespondenz');

    cy.getBySel('basis-info__kunde__select')
      .should('contain', 'Gabriela Bavarai');

    cy.getBySel('basis-info__vertraege__select')
      .should('contain', 'Frau Bavarais Vertrag');

    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'Accent GmbH & Co. KG');

    cy.getBySel('basis-info__vertriebsweg__select')
      .should('contain', 'Accent GmbH & Co. KG');

    cy.getBySel('basis-info__sparte__select')
      .should('contain', 'Förderdarlehen');

    cy.getBySel('form-files__bestehende-dokumente')
      .should('contain', 'ABC (+contract, +company)')
      .should('contain', 'DEF (-contract, -company)');

    cy.mockPwResponse('get', '/stammdaten/api/ansprechpartner/getAnsprechpartner', '{"status":"success","data":[{"id":"20","sex_descriptor":"Frau","firstname":"Maria","lastname":"Farwick (Bankprodukte)","email":"<EMAIL>","fax":"040 ********","phone_business":"040 ********","phone_mobile":"","homepage":"http:\\/\\/","address":"Holzdamm 53 - 20099 Hamburg","department":""}]}');

    // check invalid data not set:
    // vertrag is from another kunde
    // vorgangTyp not selectable with versandart
    // gesellschaft when vertrag is in query
    cy.visit('?vorgangAnlegen[kunde]=1&vorgangAnlegen[vertraege]=4&vorgangAnlegen[vorgangstyp]=120&vorgangAnlegen[versandart]=email&vorgangAnlegen[gesellschaft]=16');

    // check if invalid fields are let empty
    cy.getBySel('basis-info__vorgangstyp__select')
      .should('contain', 'Keine Auswahl');

    cy.getBySel('basis-info__kunde__select')
      .should('contain', 'Max Mustermann');

    cy.getBySel('basis-info__vertraege__select')
      .find('svg.fa-spin')
      .should('not.exist');
    cy.getBySel('basis-info__vertraege__select')
      .should('not.contain', 'Frau Bavarais Vertrag');

    cy.getBySel('basis-info__gesellschaft__select')
      .should('contain', 'Keine Auswahl');

    cy.getBySel('basis-info__vertriebsweg__select')
      .should('contain', 'Keine Auswahl');

    // check field locking
    cy.visit('?vorgangAnlegen[lock]=kunde,vertraege,gesellschaft,sparte&vorgangAnlegen[kunde]=3&vorgangAnlegen[vertraege]=4');

    cy.getBySel('basis-info__kunde__select')
      .should('contain', 'Gabriela Bavarai')
      .find('button')
      .should('have.class', 'cursor-not-allowed'); // button is currently not disabled if select is disabled
    cy.getBySel('basis-info__vertraege__select')
      .should('contain', 'Frau Bavarais Vertrag')
      .find('button')
      .should('have.class', 'cursor-not-allowed');
    cy.getBySel('basis-info__gesellschaft__select')
      .find('button')
      .should('have.class', 'cursor-not-allowed');
    cy.getBySel('basis-info__sparte__select')
      .find('button')
      .should('have.class', 'cursor-not-allowed');
  });
});
