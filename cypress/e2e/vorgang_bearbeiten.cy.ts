import addDays from 'date-fns/addDays';
import subDays from 'date-fns/subDays';

import { KorrespondenzResource, VorgangResource } from '@/store/resources/types';
import { Document } from '@/types/jsonapi';

describe('Vorgang bearbeiten', () => {
  it('can edit vorgang data', () => {
    // create vorgang
    cy.fixture<VorgangResource>('vorgaenge/korrespondenz')
      .then((korrespondenz) => {
        cy.mockPwResponse('post', '/api/mailer/forUser/1/send', '{"status":"success","data":{"from":{"address":"<EMAIL>","name":"tester"},"to":[{"address":"<EMAIL>","name":"tester"}],"cc":[{"address":"<EMAIL>","name":"tester"}],"bcc":[{"address":"<EMAIL>","name":"tester"}]}}');

        korrespondenz.attributes.faelligAt = addDays(new Date(), 1).toISOString();
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        delete korrespondenz.relationships.vertraege;

        cy.request<Document<KorrespondenzResource>>('POST', '/api/vorgaenge', { data: korrespondenz })
          .then((response) => {
            expect(response.status).to.eq(201);
            expect(response).to.have.property('body');
          });
      });

    // open vorgang
    cy.visit('/');
    cy.getBySel('vorgangsliste')
      .within(() => {
        cy.getBySel('vorgangsliste__item')
          .should('have.length', 1)
          .first()
          .as('listElement')
          .click();
      });

    // test initial timeline
    cy.getBySel('vorgang__timeline__element')
      .should('have.length', 1)
      .first()
      .should('contain.text', 'Tester 1');

    // test initial status
    cy.getBySel('vorgang__status')
      .should('have.text', 'Offen');
    cy.get('@listElement')
      .should('contain.text', 'Offen');

    // ensure only 2 statuses are visible
    cy.getBySel('vorgang__status')
      .click();
    cy.get('[data-popper-placement]')
      .should('have.length', 1)
      .find('button:visible')
      .should('have.length', 2);
    cy.getBySel('vorgang__status')
      .click();

    // change status to "Erledigt"
    cy.selectFromDsSelectByLabel('vorgang__status', 'Erledigt');
    cy.getBySel('vorgang__status')
      .should('have.text', 'Erledigt');
    cy.getBySel('vorgang__timeline__element')
      .should('have.length', 2)
      .last()
      .should('contain.text', 'Status')
      .and('contain.text', 'Erledigt')
      .and('contain.text', 'Tester 1');
    cy.get('@listElement')
      .should('contain.text', 'Erledigt');

    // change status back to "Offen"
    cy.selectFromDsSelectByLabel('vorgang__status', 'Offen');
    cy.getBySel('vorgang__status')
      .should('have.text', 'Offen');
    cy.getBySel('vorgang__timeline__element')
      .should('have.length', 3)
      .last()
      .should('contain.text', 'Status')
      .and('contain.text', 'Offen')
      .and('contain.text', 'Tester 1');
    cy.get('@listElement')
      .should('contain.text', 'Offen');

    // test bearbeiter
    cy.getBySel('vorgang__sidebar__bearbeiter')
      .should('exist')
      .within(() => {
        cy.getBySel('user-tag')
          .should('have.length', 1)
          .eq(0)
          .should('have.text', 'Tester 1');
      })
      .findBySel('vorgang__sidebar__cta')
      .click();
    cy.get('[data-popper-placement]')
      .should('have.length', 1)
      .within(() => {
        cy.get('button').should('have.length', 5);

        cy.get('input')
          .should('exist')
          .type('1{enter}{backspace}');

        cy.contains('button', 'Tester 2')
          .should('exist')
          .click();

        cy.get('input')
          .should('exist')
          .type('{esc}');
      });
    cy.getBySel('vorgang__timeline__element')
      .should('have.length', 5)
      .eq(3)
      .should('contain.text', 'Tester 1')
      .and('contain.text', 'Bearbeiter');
    cy.getBySel('vorgang__timeline__element')
      .last()
      .should('contain.text', 'Tester 1')
      .and('contain.text', 'Tester 2')
      .and('contain.text', 'Bearbeiter');

    // test beobachter
    cy.getBySel('vorgang__sidebar__beobachter')
      .should('exist')
      .within(() => {
        cy.getBySel('user-tag')
          .should('have.length', 1)
          .eq(0)
          .should('have.text', 'Tester 1');
      })
      .findBySel('vorgang__sidebar__cta')
      .click();

    cy.get('[data-popper-placement]')
      .should('have.length', 1)
      .within(() => {
        cy.get('button').should('have.length', 5);

        cy.contains('button', 'Tester 1')
          .should('exist')
          .click();

        cy.contains('button', 'Tester 3')
          .should('exist')
          .click();

        cy.get('input')
          .should('exist')
          .type('{esc}');
      });
    cy.getBySel('vorgang__timeline__element')
      .should('have.length', 7)
      .eq(5)
      .should('contain.text', 'Tester 1')
      .and('contain.text', 'Beobachter');
    cy.getBySel('vorgang__timeline__element')
      .last()
      .should('contain.text', 'Tester 1')
      .and('contain.text', 'Tester 3')
      .and('contain.text', 'Beobachter');

    // test faelligkeit
    cy.getBySel('vorgang__sidebar__faelligkeit')
      .should('contain.text', 'morgen');
    cy.get('@listElement')
      .should('contain.text', 'morgen');

    // try set gestern as faelligkeit
    cy.setDateInDsDatepicker(
      'vorgang__sidebar__faelligkeit__datepicker',
      subDays(new Date(), 1),
    );
    cy.getBySel('vorgang__sidebar__faelligkeit')
      .should('contain.text', 'morgen');
    cy.get('@listElement')
      .should('contain.text', 'morgen');

    // set uebermorgen as faelligkeit
    cy.setDateInDsDatepicker(
      'vorgang__sidebar__faelligkeit__datepicker',
      addDays(new Date(), 2),
    );
    cy.getBySel('vorgang__sidebar__faelligkeit')
      .should('not.contain.text', 'morgen');
    cy.get('@listElement')
      .should('not.contain.text', 'morgen');
    cy.getBySel('vorgang__timeline__element')
      .should('have.length', 8)
      .last()
      .should('contain.text', 'Tester 1')
      .and('contain.text', 'Fälligkeitsdatum');

    // add vertrag to vorgang
    cy.getBySel('vorgang__sidebar__add-vertrag')
      .click();
    cy.getBySel('vorgang__add-vertrag__modal')
      .should('exist');
    cy.contains('button', 'Übernehmen')
      .should('be.disabled');
    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'Bank Produkt Herr UND Frau Musterfamilie');
    cy.contains('button', 'Übernehmen')
      .click();
    cy.getBySel('vorgang__add-vertrag__modal')
      .should('contain.text', 'Bank Produkt Herr UND Frau Musterfamilie');
    cy.contains('button', 'Übernehmen')
      .click();

    cy.getBySel('vorgang__sidebar__add-vertrag')
      .should('not.exist');
    cy.getBySel('vorgang__sidebar__vertrag')
      .should('contain.text', 'Bank Produkt Herr UND Frau Musterfamilie');
    cy.getBySel('vorgang__timeline__element')
      .should('have.length', 9)
      .last()
      .should('contain.text', 'Tester 1 hat dem Vorgang einen Vertrag hinzugefügt');


    // test title changing
    cy.getBySel('vorgang__titel')
      .should('contain.text', 'Ablauf (M-K)');
    cy.getBySel('vorgang__edit-titel__button').first()
      .click();
    cy.getBySel('vorgang__titel-form__input').find('input')
      .should('contain.value', 'Ablauf (M-K)')
      .clear();
    cy.getBySel('vorgang__titel-form__submit-button').find('button')
      .should('be.disabled');

    cy.getBySel('vorgang__titel-form__input').find('input')
      .type('hello world');
    cy.getBySel('vorgang__titel-form__abort-button').first()
      .click();
    cy.getBySel('vorgang__titel')
      .should('contain.text', 'Ablauf (M-K)');

    cy.getBySel('vorgang__edit-titel__button').first()
      .click();
    cy.getBySel('vorgang__titel-form__input').find('input')
      .should('contain.value', 'Ablauf (M-K)')
      .clear()
      .type('hello world');
    cy.getBySel('vorgang__titel-form__submit-button').first()
      .click();
    cy.getBySel('vorgang__titel')
      .should('contain.text', 'hello world');
    cy.getBySel('vorgang__vorgangstyp')
      .should('contain.text', 'Ablauf (M-K)');
    cy.get('@listElement')
      .should('contain.text', 'hello world');
    cy.getBySel('vorgang__edit-titel__button').first()
      .click();
    cy.getBySel('vorgang__titel-form__input').find('input')
      .should('contain.value', 'hello world');

    cy.reload();
    cy.getBySel('vorgang__titel')
      .should('contain.text', 'hello world');
  });
});
