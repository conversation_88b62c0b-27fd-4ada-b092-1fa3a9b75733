describe('Vorgang Dokumente', () => {
  it('can filter and select documents', () => {
    const mock = {
      status: 'success',
      data: {
        limit: 100,
        offset: null,
        agencyId: 1,
        count: 4,
        overallCount: 4,
        data: [
          {
            id: 1,
            name: 'ABC (+contract, +company)',
            extension: 'pdf',
            type: 50,
            contract: 13485,
            company: 156,
            product: null,
            createdAt: '2022-01-09',
          },
          {
            id: 2,
            name: 'DEF (-contract, -company)',
            extension: 'pdf',
            type: 50,
            contract: null,
            company: null,
            product: null,
            createdAt: '2022-01-10',
          },
          {
            id: 3,
            name: 'GHI (+contract, -company)',
            extension: 'pdf',
            type: 50,
            contract: 13485,
            company: null,
            product: null,
            createdAt: '2022-01-11',
          },
          {
            id: 4,
            name: 'JKL (-contract, +company)',
            extension: 'pdf',
            type: 50,
            contract: null,
            company: 156,
            product: null,
            createdAt: '2022-01-12',
          },
        ],
      },
    };
    cy.mockPwResponse('get', 'home/kundenakte/2/dokumente', JSON.stringify(mock), 2);

    cy.get('button').contains('Vorgang anlegen').first().click();

    cy.selectFromDsSelectBySearch('basis-info__kunde__select', 'Maria Musterfrau');
    cy.selectFromDsMultiselectByLabel('basis-info__vertraege__select', 'Schaden-13485');

    cy.get('button').contains('Aus Dokumenten auswählen').first().click();

    cy.getBySel('select-existing-documents__modal')
      .should('contain', 'Bestehende Dokumente anhängen')
      .within(() => {
        cy.get('.vue-virtualized-list__row')
          .should('have.length', 4)
          .first()
          .should('contain', 'ABC (+contract, +company)')
          .find('input[type=checkbox]')
          .parent()
          .click();

        cy.get('button')
          .last()
          .should('contain', '1 Dokument anhängen')
          .click();
      });

    cy.selectFromDsSelect('basis-info__vorgangstyp__select', 5);

    cy.addNewItemToDsMultiselect('step1__empfaenger__form', '<EMAIL>');

    cy.get('[data-test="vorgang-anlegen__nachricht__betreff"] .ProseMirror')
      .first()
      .type('Testvorgang mit Dokumenten - Betreff');

    cy.get('[data-test="vorgang-anlegen__nachricht__inhalt"] .ProseMirror')
      .type('Testvorgang mit Dokumenten - Inhalt');

    cy.intercept({
      method: 'POST',
      url: '/api/vorgaenge',
    }).as('vorgangApiCall');
    cy.get('button').contains('Zur Vorschau').first().click();
    cy.wait('@vorgangApiCall').then((interception) => {
      assert.equal(interception.response?.statusCode, 201);
    });

    cy.getBySel('vorgang-anlegen__modal')
      .get('button')
      .last()
      .should('have.text', 'Vorgang anlegen & E-Mail versenden');

    cy.get('div')
      .contains('Anhänge')
      .should('have.length', 1)
      .parent()
      .next()
      .should('contain', 'ABC (+contract, +company)');
  });
});
