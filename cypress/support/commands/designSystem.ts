Cypress.Commands.add('selectFromDsSelect', (selector, idx) => {
  // wait until loading finished
  cy.getBySel(selector)
    .find('svg.fa-spin')
    .should('not.exist');

  cy.getBySel(selector)
    .click();

  cy.get('[data-popper-placement]')
    .filter(':visible')
    .should('have.length', 1)
    .find('button')
    .as('btns')
    .should('have.length.at.least', idx + 1);

  cy.get('@btns')
    .eq(idx)
    .click();
});

Cypress.Commands.add('clearDsSelect', (selector) => {
  // wait until loading finished
  cy.getBySel(selector)
    .find('svg.fa-spin')
    .should('not.exist');

  cy.getBySel(selector)
    .type('{backspace}{esc}');
});

Cypress.Commands.add('selectFromDsSelectByLabel', (selector, label) => {
  // wait until loading finished
  cy.getBySel(selector)
    .find('svg.fa-spin')
    .should('not.exist');

  cy.getBySel(selector)
    .click();

  cy.get('[data-popper-placement]')
    .filter(':visible')
    .should('have.length', 1)
    .contains('button', label)
    .should('exist')
    .click();
});

Cypress.Commands.add('selectFromDsSelectBySearch', (selector, query) => {
  // wait until loading finished
  cy.getBySel(selector)
    .find('svg.fa-spin')
    .should('not.exist');

  cy.getBySel(selector)
    .click();

  cy.get('[data-popper-placement]')
    .filter(':visible')
    .should('have.length', 1)
    .within(() => {
      cy.get('input')
        .should('exist')
        .type(query);

      cy.get('button')
        .should('have.length.at.least', 1)
        .eq(0)
        .click();
    });
});

Cypress.Commands.add('addNewItemToDsMultiselect', (selector, newItem) => {
  cy.getBySel(selector)
    .find('svg.fa-spin')
    .should('not.exist');

  cy.getBySel(selector)
    .type(newItem)
    .type('{enter}{esc}');

  // wait until closed
  cy.get('[data-popper-placement]:visible')
    .should('not.exist');
});

Cypress.Commands.add('selectFromDsMultiselectByLabel', (selector, ...labels) => {
  // wait until loading finished
  cy.getBySel(selector)
    .find('svg.fa-spin')
    .should('not.exist');

  cy.getBySel(selector).within(() => {
    cy.get('input').click();
  });

  labels.forEach((label) => {
    cy.get('[data-popper-placement]')
      .filter(':visible')
      .should('have.length', 1)
      .contains('button', label)
      .should('exist')
      .click();
  });

  // close dropdown
  cy.getBySel(selector).within(() => {
    cy.get('input').type('{esc}');
  });

  // wait until closed
  cy.get('[data-popper-placement]:visible')
    .should('not.exist');
});

Cypress.Commands.add('setDateInDsDatepicker', (selector, date) => {
  cy.getBySel(selector)
    .find('input:hidden')
    .not('.border') // ignore input that is not for testing
    .should('have.length', 1)
    .then(($input) => {
      $input.show();
    })
    .clear()
    .type(date.toISOString())
    .blur()
    .then(($input) => {
      $input.hide();
    });
});
