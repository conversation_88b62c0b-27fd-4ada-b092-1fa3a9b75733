Cypress.Commands.add('mockPwResponse', (
  method,
  url,
  responseBody,
  count = 1,
) => {
  for (let i = 0; i < count; i++) {
    cy.request({
      method: 'POST',
      url: 'testing/pwMockResponse',
      form: true,
      body: {
        'method': method,
        'url': url,
        'body': responseBody,
      },
    });
  }
});

Cypress.Commands.add('login', () => {
  return cy
    .fixture<string>('token/login_token')
    .then((token) => (
      cy.visit(`login/pw/callback?token=${token}`)
    ));
});

Cypress.Commands.add('logout', () => {
  return cy.request({
    url: '/logout',
    followRedirect: false,
  });
});

Cypress.Commands.add('getBySel', (selector, ...args) => {
  return cy.get(`[data-test="${selector}"]`, ...args);
});

Cypress.Commands.add(
  'findBySel',
  { prevSubject: true },
  (subject: Cypress.Chainable, selector, ...args) => {
    return subject.find(`[data-test="${selector}"]`, ...args);
  },
);

Cypress.Commands.add('containsBySel', (selector: string, ...args) => {
  return cy.contains(`[data-test="${selector}"]`, ...args);
});
