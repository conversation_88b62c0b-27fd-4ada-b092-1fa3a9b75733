function buildArtisanCommand(command: string): string {
  return `${Cypress.env('command_prefix')} php artisan ${command} ${Cypress.env('command_suffix')}`;
}

Cypress.Commands.add('clearCache', () => {
  cy.exec(buildArtisanCommand('cache:clear'));
});

Cypress.Commands.add('refreshDb', () => {
  cy.exec(buildArtisanCommand('snapshot:load cypress-dump'), { failOnNonZeroExit: false })
    .its('stdout')
    .then((stdout) => {
      if (!stdout.includes('SQLSTATE') && !stdout.includes('No snapshots found')) {
        cy.log('Snapshot found! Using snapshot...');

        return;
      }

      cy.log('No snapshot found. Seeding database...');

      cy.exec(buildArtisanCommand('migrate:fresh'));

      cy.exec(buildArtisanCommand('vorgaenge:sync:firmenAndUsers'));
      cy.exec(buildArtisanCommand('vorgaenge:sync:kunden'));
      cy.exec(buildArtisanCommand('vorgaenge:sync:vertraege'));
      cy.exec(buildArtisanCommand('vorgaenge:sync:schaeden'));
      cy.exec(buildArtisanCommand('job:dispatch SyncGesellschaften'));
      cy.exec(buildArtisanCommand('job:dispatch SyncSparten'));
      cy.exec(buildArtisanCommand('job:dispatch SyncDokumentTypen'));

      cy.exec(buildArtisanCommand('snapshot:create cypress-dump'));
    });
});
