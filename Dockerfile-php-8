# syntax=docker/dockerfile:experimental
FROM composer:2 as COMPOSER
ENV COMPOSER_HOME=/usr/config/composer
ARG GITHUB_TOKEN
RUN composer config -g github-oauth.github.com $GITHUB_TOKEN

FROM php:8.0-cli

ENV COMPOSER_HOME=/usr/config/composer
ARG USER_ID
ARG GROUP_ID

RUN usermod -u $USER_ID www-data && groupmod -g $GROUP_ID www-data

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y git

RUN cd /usr/local/etc/php/conf.d/ && \
  echo 'memory_limit = -1' >> /usr/local/etc/php/conf.d/docker-php-memlimit.ini

#install some base extensions
RUN apt-get install -y \
        libzip-dev \
        zip \
  && docker-php-ext-install zip

# Install composer and dependencies
COPY --chown=www-data:www-data --from=COMPOSER /usr/bin/composer /usr/bin/composer
COPY --chown=www-data:www-data --from=COMPOSER /usr/config/composer /usr/config/composer

# Make sure composer can checkout github
RUN mkdir -p /var/www/.ssh/ && \
    touch /var/www/.ssh/known_hosts && \
    ssh-keyscan github.com >> /var/www/.ssh/known_hosts

WORKDIR /var/www/html

USER www-data
