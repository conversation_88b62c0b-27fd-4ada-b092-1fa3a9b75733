openapi: 3.0.2
info:
  title: Vorgaenge API
  description: This is the API for the Vorgaenge DEMV Module
  version: 0.0.1
components:
  schemas:
    Vorgaenge:
      type: array
      items:
        $ref: "#/components/schemas/Vorgang"
    Vorgang:
      type: object
      properties:
        id:
          type: integer
          example: '2'
        type:
          type: string
        attributes:
          $ref: "#/components/schemas/VorgangAttributes"
        links:
          type: object
          properties:
            self:
              type: string
              example: 'http://vorgaenge.demv.internal/api/vorgaenge/2'
    VorgangAttributes:
      type: object
      properties:
        titel:
          type: string
          example: 'Angebotsanfrage Risikoleben'
        vorgangsnummer:
          type: string
          example: 'Vka-621911'
        status:
          type: string
          enum: [ entwurf, offen, erinnerung_1, erinnerung_2, mahnung_1, mahnung_2, erledigt ]
          example: offen
        is_wichtig:
          type: boolean
        user_id:
          type: integer
        kunde_id:
          type: integer
        gesellschaft_id:
          type: integer
        faellig_at:
          type: string
          format: date
          example: '2021-05-16T19:13:37+00:00'
        created_at:
          type: string
          format: date
          example: '1994-03-10T19:13:37+00:00'
        updated_at:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
    Vertraege:
      type: array
      items:
        $ref: "#/components/schemas/Vertrag"
    Vertrag:
      type: object
      properties:
        id:
          type: integer
          example: '2'
        type:
          type: string
          example: vertraege
        attributes:
          $ref: "#/components/schemas/VertragAttributes"
        links:
          type: object
          properties:
            self:
              type: string
              example: 'http://vorgaenge.demv.internal/api/vertraege/2'
    VertragAttributes:
      type: object
      properties:
        vertragsnummer:
          type: string
          example: '2a5a6016-9a89-36de-8b34-7e8b4c906693'
        createdAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
        updatedAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
    Users:
      type: array
      items:
        $ref: "#/components/schemas/User"
    User:
      type: object
      properties:
        id:
          type: integer
          example: '2'
        type:
          type: string
          example: users
        attributes:
          $ref: "#/components/schemas/UserAttributes"
        links:
          type: object
          properties:
            avatar:
              type: string
              example: 'http://vorgaenge.demv.internal/api/users/110/avatar.svg'
    UserAttributes:
      type: object
      properties:
        name:
          type: string
          example: 'Maria Musterfrau'
        createdAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
        updatedAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
    TimelineEintraege:
      type: array
      items:
        $ref: "#/components/schemas/TimelineEintrag"
    TimelineEintrag:
      type: object
      properties:
        property:
          type: integer
    Sparten:
      type: array
      items:
        $ref: "#/components/schemas/Sparte"
    Sparte:
      type: object
      properties:
        id:
          type: integer
          example: '2'
        type:
          type: string
          example: sparten
        attributes:
          $ref: "#/components/schemas/SparteAttributes"
        links:
          type: object
          properties:
            self:
              type: string
              example: 'http://vorgaenge.demv.internal/api/sparten/2'
    SparteAttributes:
      type: object
      properties:
        name:
          type: string
          example: 'Bankprodukte->Förderdarlehen'
        createdAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
        updatedAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
        deletedAt:
          type: string
          format: date
          example: null
    Kunden:
      type: array
      items:
        $ref: "#/components/schemas/Kunde"
    Kunde:
      type: object
      properties:
        id:
          type: integer
          example: 51545
        type:
          type: string
          example: kunden
        attributes:
          $ref: "#/components/schemas/KundeAttributes"
        links:
          type: object
          properties:
            self:
              type: string
              example: 'http://vorgaenge.demv.internal/api/kunden/51545'
    KundeAttributes:
      type: object
      properties:
        name:
          type: string
          example: 'Max Musterfrau'
        createdAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
        updatedAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
    Gesellschaften:
      type: array
      items:
        $ref: "#/components/schemas/Gesellschaft"
    Gesellschaft:
      type: object
      properties:
        type:
          type: string
          example: 'gesellschaften'
        id:
          type: string
          example: '1'
        attributes:
          $ref: '#/components/schemas/GesellschaftAttributes'
        links:
          type: object
          properties:
            self:
              type: string
              example: 'http://vorgaenge.demv.internal/api/gesellschaften/1'
    GesellschaftAttributes:
      type: object
      properties:
        name:
          type: string
          example: 'AachenMünchener'
        createdAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
        updatedAt:
          type: string
          format: date
          example: '2000-05-16T19:13:37+00:00'
    Firmen:
      type: array
      items:
        $ref: "#/components/schemas/Firma"
    Firma:
      type: object
      properties:
        property:
          type: integer
    FaelligkeitAenderungen:
      type: array
      items:
        $ref: "#/components/schemas/FaelligkeitAenderungen"
    FaelligkeitAenderung:
      type: object
      properties:
        property:
          type: integer
    PrioritaetAenderungen:
      type: array
      items:
        $ref: "#/components/schemas/PrioritaetAenderungen"
    PrioritaetAenderung:
      type: object
      properties:
        property:
          type: integer
    StatusAenderungen:
      type: array
      items:
        $ref: "#/components/schemas/StatusAenderungen"
    StatusAenderung:
      type: object
      properties:
        property:
          type: integer
    TitelAenderungen:
      type: array
      items:
        $ref: "#/components/schemas/TitelAenderungen"
    TitelAenderung:
      type: object
      properties:
        property:
          type: integer
    Korrespondenzen:
      type: array
      items:
        $ref: "#/components/schemas/Korrespondenz"
    Korrespondenz:
      type: object
      properties:
        versandart:
          type: string
          enum: [ email, brief ]
          example: brief
        betreff:
          type: string
        content:
          type: array
        absender:
          type: array
        empfaenger:
          type: array
        versendetAt:
          type: string
          example:
    Verknuepfungen:
      type: array
      items:
        $ref: "#/components/schemas/Verknuepfung"
    Verknuepfung:
      type: object
      properties:
        property:
          type: integer
  responses:
    Unauthorized:
      description: Unauthorized
servers:
  - url: https://vorgaenge.demv.internal/api
    description: Local environment
paths:
  /gesellschaften:
    get:
      description:
      responses:
        '200':
          description: Gibt alle Gesellschaften zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: '#/components/schemas/Gesellschaften'
        401:
          $ref: '#/components/responses/Unauthorized'
  /gesellschaften/{gesellschaft}:
    get:
      description:
      responses:
        '200':
          description: Gibt eine Gesellschaft zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: "#/components/schemas/Gesellschaft"

        '404':
          description: Not Found
        401:
          $ref: '#/components/responses/Unauthorized'
  /kunden:
    get:
      description:
      responses:
        '200':
          description: Gibt alle Kunden zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: '#/components/schemas/Kunden'
        401:
          $ref: '#/components/responses/Unauthorized'
  /kunden/{kunde}:
    get:
      description:
      responses:
        '200':
          description: Gibt einen Kunden zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: "#/components/schemas/Kunde"
        '404':
          description: Not Found
        401:
          $ref: '#/components/responses/Unauthorized'
  /sparten:
    get:
      description:
      responses:
        '200':
          description: Gibt alle Sparten zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: '#/components/schemas/Sparten'
        401:
          $ref: '#/components/responses/Unauthorized'
  /hierarchy:
    get:
      description:
      responses:
        '200':
          description: Gibt alle Möglichen Bearbeiter zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: '#/components/schemas/Users'
        401:
          $ref: '#/components/responses/Unauthorized'
  /sparten/{sparte}:
    get:
      description:
      responses:
        '200':
          description: Gibt eine Sparte zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: "#/components/schemas/Sparte"
        '404':
          description: Not Found
        401:
          $ref: '#/components/responses/Unauthorized'
  /vertraege:
    get:
      description:
      responses:
        '200':
          description: Gibt alle Vertraege zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: '#/components/schemas/Vertraege'
        401:
          $ref: '#/components/responses/Unauthorized'
  /vertraege/{vertrag}:
    get:
      description:
      responses:
        '200':
          description: Gibt einen Vertrag zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: "#/components/schemas/Vertrag"
        '404':
          description: Not Found
        401:
          $ref: '#/components/responses/Unauthorized'
  /vorgaenge:
    post:
      description:
      responses:
        '200':
          description: TODO
        401:
          $ref: '#/components/responses/Unauthorized'
    get:
      description:
      responses:
        '200':
          description: Gibt alle Vorgänge zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: '#/components/schemas/Vorgaenge'
        401:
          $ref: '#/components/responses/Unauthorized'
  /vorgaenge/{vorgang}:
    put:
      description:
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  type: object
                  properties:
                    type:
                      type: string
                      example: 'vorgaenge'
                    attributes:
                      type: object
                      properties:
                        titel:
                          type: string
                          example: 'Anfrage 123'
                        faelligAt:
                          type: string
                          example: '2021-06-25'
                        status:
                          type: string
                          enum: [ entwurf, offen, erinnerung_1, erinnerung_2, mahnung_1, mahnung_2, erledigt ]
                          example: offen
                        isWichtig:
                          type: boolean
      responses:
        '200':
          description: TODO
        401:
          $ref: '#/components/responses/Unauthorized'
    patch:
      description:
      responses:
        '200':
          description: TODO
        401:
          $ref: '#/components/responses/Unauthorized'
    get:
      description:
      responses:
        '200':
          description: Gibt einen spezifischen Vorgang zurück
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                    $ref: "#/components/schemas/Vorgang"
        '404':
          description: Not Found
        401:
          $ref: '#/components/responses/Unauthorized'
  /vorgaenge/{vorgang}/korrespondenzen:
    post:
      description: Legt eine Korrespondenz an einem Vorgang an
      requestBody:
        description: Korrenspondenz die einem Vorgang hinzugefügt werden soll
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Korrespondenz'
      responses:
        '200':
          description: TODO
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Korrespondenz'
        401:
          $ref: '#/components/responses/Unauthorized'
  /vorgaenge/{vorgang}/verknuepfungen:
    post:
      description:
      responses:
        '200':
          description: TODO
        401:
          $ref: '#/components/responses/Unauthorized'
  /vorgaenge/{vorgang}/verknuepfungen/{verknuepfung}:
    delete:
      description:
      responses:
        '200':
          description: TODO
        401:
          $ref: '#/components/responses/Unauthorized'
  /{type}/{id}/avatar.svg:
    get:
      description:
      summary:
      parameters:
        - in: path
          name: type
          required: true
          schema:
            type: string
            example: 'users'
        - in: path
          name: id
          required: true
          schema:
            type: integer
            example: 79
      responses:
        '200':
          description: TODO
        '404':
          description: Not Found
        401:
          $ref: '#/components/responses/Unauthorized'
