---
alwaysApply: true
---
# About running commands

- We use docker compose
- Services run in docker containers, see [docker-compose.yml](mdc:docker-compose.yml)
- We use [just](https://github.com/casey/just) to run commands.
- You can give arguments to commands like this: `just <command> <arguments>`
- BEFORE running any commands: Always run `just` to see how to connect to those containers and run commands like lint, test, artisan, yarn and more!

