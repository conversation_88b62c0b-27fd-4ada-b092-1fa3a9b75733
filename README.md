# ProfessionalWorks SDK

## Intention of this Package

Use Endpoints directly. Simple functions return Object for easy access. 

With laravel: configure, use endpoints via Dependency Injection and go!

Don't hassle with errors, json parsing, validating PW responses....

Just get a simple Object to work with (or an ProfessionalWorksSdkException Implementation) if something fails.

## Example:
```php
class VertraegeSynchronizer
{
    public function __construct(
        private VertraegeEndpoint $vertraegeEndpoint,
    ) {
    }
    
    public function syncVertrage(int $companyId){
        $vertrage = $this->vertraegeEndpoint->fetchAllForFirma($companyId);
        
        foreach($vertraege as $vertrag){
        /// do something
        }
    }
    
```
You wonder "This gives me only the first 25 Contracts of a company, on the first page of the PW result?"

You will be surprised! - It will  indeed give you all contracts of a company - not 25, not 100 - not 1000 - all of them!  - it fetches the different pages in the background for you!

But wait - there is more: It is also low on memory: It uses generators for this!

Already convinced? 
    
Then just move forward to the setup:
## Setup

### Composer

```composer config repositories.PW_<NAME_EMAIL>:demvsystems/professionalworks-sdk.git```

```composer config repositories.SDK_<NAME_EMAIL>:demvsystems/sdk-framework.git```

```composer require demv/professionalworks-sdk:dev-main```


### within Laravel

To create the config either

* run ```artisan vendor:publish```and select the ProfessionalWorks-SDK to publish the config to your application

or

* copy the config/professionalworks_sdk.php file into your Laravel Config directory.

Now the Gateway is available and the provided Endpoints can be used to access the ProfessionalWorks APIs. 

With a Laravel Application and given, you adapted the config file, you can now use the SDK.

The preferred way of usage is to use the Endpoints directly. The PW connection is handled automatically via Laravel Dependency Injection


You may need to check, that your environment variables are set correctly or adapt the config to your needs.

<table>
<tr><th>Config-Key</th><th>default value</th><th>Description</th></tr>
<tr><td>baseUrl</td><td>http://professionalworks.local</td><td>The Base URL where PW is reachable</td></tr>
<tr><td>token</td><td></td><td>The Token to used to access PW - preferably an Admin Token created on an PW-Account for your application</td></tr>
<tr><td>origin</td><td></td><td>The Name of your Project. Used for Identifying Requests on PW side.</td></tr>
<tr><td>oauth:</td><td></td><td></td></tr>
<tr><td>loginUrl</td><td>auth/shortlived/token</td><td>TODO: Isn't this fix?</td></tr>
<tr><td>publicKey</td><td></td><td>The Public Key of your PW Instance</td></tr>
<tr><td>proxy:</td><td></td><td></td></tr>
<tr><td>user</td><td></td><td>Proxy User to access your PW Instance (if needed)</td></tr>
<tr><td>password</td><td></td><td>Proxy Password to access your PW Instance (if needed)</td></tr>
<tr><td>baseUrl</td><td></td><td>Proxy URL to access your PW Instance (if needed)</td></tr>
</table>

### within a general PHP Application

Simply instantiate the gateway as follows:

```php 
    $productIdentifier = 'exampleProduct',
    $professionalworksGateway = new ProfessionalworksGateway(
        $gatewayConfig['baseUrl'],
        $gatewayConfig['token'],
        $productIdentifier,
        $gatewayConfig['proxy']['user'] ?? '',
        $gatewayConfig['proxy']['password'] ?? '',
        $gatewayConfig['proxy']['baseUrl'] ?? '',
    )
                 

 ```

```php 
    $oauthService = new OAuthService($gatewayConfig['oauth']['publicKey'], $professionalworksGateway);
```

The different endpoints are grouped, extend the AbstractEndpoint::class and can be used like this:
Each endpoint provides individual functionality based on the accessible data.

```php 
    $endpoint = new LogoEndpoint($professionalworksGateway);
    $path = $endpoint->getImagePath($userId);
```
# Structure

## Code

//TODO
### Endpoints
//TODO
### Gateway
//TODO
### OAuth
//TODO

## Documentation / Development

[Have a look at the README for the Documentation](https://github.com/demvsystems/sdk-documentation)

