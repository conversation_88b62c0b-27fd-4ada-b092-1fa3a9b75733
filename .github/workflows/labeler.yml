# add labels to a new PR based on .github/labeler.yml config
name: Labeling PR

on:
  - pull_request_target

jobs:
  labeler:
    if: ${{ !startsWith(github.head_ref, 'dependabot/') }}
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/labeler@v5
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          sync-labels: true
