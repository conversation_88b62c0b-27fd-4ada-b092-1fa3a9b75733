name: CI

on: [push]

concurrency:
  group: ${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  phpunit:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        operating-system: [ 'ubuntu-latest' ]
        php-versions: [ '8.0', '8.1', '8.2', '8.3' ]

    services:
      mysql:
        image: mysql
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: yes
          MYSQL_DATABASE: phpunit
        ports:
          - 3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - uses: actions/checkout@v2

      - name: Setup PHP with composer v2
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          tools: composer:v2
          extensions: :opcache
          coverage: none
        env:
          COMPOSER_TOKEN: ${{ secrets.PRIVATE_TOKEN }}

      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('backend/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install private repo token
        run: composer config --global github-oauth.github.com ${{ secrets.PRIVATE_TOKEN }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-progress --no-suggest --optimize-autoloader

      - name: Run tests
        run: vendor/bin/phpunit --do-not-cache-result


  lint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Setup PHP with composer v2
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.0'
          tools: composer:v2
          coverage: none

      - name: Install private repo token
        run: composer config --global github-oauth.github.com ${{ secrets.PRIVATE_TOKEN }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-progress --no-suggest --optimize-autoloader

      - name: Lint
        run: vendor/bin/phpcstd --ci
