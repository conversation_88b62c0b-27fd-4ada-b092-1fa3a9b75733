name: CI

on: [ push ]

concurrency:
  group: ${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  phpunit:
    runs-on: ubuntu-latest
    timeout-minutes: 20

    services:
      mysql:
        image: mysql
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: yes
          MYSQL_DATABASE: cypress
        ports:
          - 33033:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      minio:
        # fixme: let's not depend on external unofficial image
        image: lazybit/minio
        ports:
          - 9000:9000
        env:
          # fixme: for lazybit/minio it needs deprecated keys
          # one should use MINIO_ROOT_USER, MINIO_ROOT_PASSWORD
          MINIO_ACCESS_KEY: minio
          MINIO_SECRET_KEY: minio123
          # MINIO_ROOT_USER: minio
          # MINIO_ROOT_PASSWORD: minio123
        options: --name=minio --health-cmd "curl http://localhost:9000/minio/health/live"

    steps:
      # We create the s3 bucket for our minio, it's a bit convoluted, there may be a better way
      # we download the mc binary, and use it to create the bucket
      - run: wget --tries=5 --timeout=60 https://dl.min.io/client/mc/release/linux-amd64/mc
      - run: chmod +x ./mc
      - run: bash -c "until (./mc alias set minio http://127.0.0.1:9000 minio minio123) do echo 'waiting for minio to become reachable' && sleep 1; done;"
      - run: ./mc mb --ignore-existing minio/demv-ci-vorgaenge
      # end of minio bucket creation

      - uses: actions/checkout@v4

      - name: Setup PHP with composer v2
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          tools: composer:v2
          extensions: :opcache
          coverage: none
          github-token: ${{ secrets.PRIVATE_TOKEN }}

      - name: Get composer cache directory
        id: composercache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('backend/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Set Composer Secret
        run: echo "COMPOSER_SECRET=${{ secrets.PRIVATE_TOKEN }}" >> $GITHUB_ENV

      - name: Set Composer Secret for Dependabot
        if: github.actor == 'dependabot[bot]'
        run: echo "COMPOSER_SECRET=${{ secrets.COMPOSER_SECRET }}" >> $GITHUB_ENV

      - name: Install private repo token
        run: composer config --global github-oauth.github.com ${{ env.COMPOSER_SECRET }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-progress --optimize-autoloader

      - name: Copy CI ENV
        run: cp .env.ci .env

      - name: Run tests
        run: vendor/bin/phpunit --configuration phpunit.ci.xml --do-not-cache-result

  lint-php:
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP with composer v2
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          tools: composer:v2
          coverage: none
          github-token: ${{ secrets.PRIVATE_TOKEN }}

      - name: Get composer cache directory
        id: composercache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('backend/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-
      - name: Set Composer Secret
        run: echo "COMPOSER_SECRET=${{ secrets.PRIVATE_TOKEN }}" >> $GITHUB_ENV

      - name: Set Composer Secret for Dependabot
        if: github.actor == 'dependabot[bot]'
        run: echo "COMPOSER_SECRET=${{ secrets.COMPOSER_SECRET }}" >> $GITHUB_ENV

      - name: Install private repo token
        run: composer config --global github-oauth.github.com ${{ env.COMPOSER_SECRET }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-progress --optimize-autoloader

      - name: Lint project
        run: vendor/bin/phpcstd --ci

      - name: Deptrac for Support Namespaces
        run: vendor/bin/deptrac analyze --config-file=./deptrac_support.yaml

      - name: Check if OpenApi Documentation is up to date
        run: |
          vendor/bin/openapi app/Domain/ApiGateway -o openapi.json
          git diff --exit-code openapi.json || (echo -e "\nDie openapi.json ist nicht aktuell, \"just openapi\" ausführen!" && exit 1)

  deploy:
    name: Deploy to Vapor
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'
    needs:
      - phpunit
      - vitest
    env:
      ASSET_URL: ""
      COMPOSER_SECRET: ${{ secrets.PRIVATE_TOKEN }}
      DEPLOY_ENVIRONMENT: staging
      VITE_PW_BASE_URL: https://preview.crm.deutscher-maklerverbund.de/
      VITE_SENTRY_DSN: https://<EMAIL>/22
      VITE_SENTRY_TRACES_SAMPLE_RATE: 0.2
      VITE_ENABLE_BUG_REPORTING: false
      VITE_APP_INTERCOM_APP_ID: ${{ secrets.VITE_APP_INTERCOM_APP_ID }}
      VITE_APP_GTM_ID: ${{ secrets.VITE_APP_GTM_ID }}

    steps:
      - name: Update env for main
        if: github.ref == 'refs/heads/main'
        run: |
          echo "DEPLOY_ENVIRONMENT=staging" >> $GITHUB_ENV
          echo "VITE_ENABLE_BUG_REPORTING=true" >> $GITHUB_ENV
          echo "VITE_PW_BASE_URL=https://preview.crm.deutscher-maklerverbund.de/" >> $GITHUB_ENV

      - name: Update env for dev
        if: github.ref == 'refs/heads/dev'
        run: |
          echo "DEPLOY_ENVIRONMENT=development" >> $GITHUB_ENV
          echo "VITE_ENABLE_BUG_REPORTING=true" >> $GITHUB_ENV
          echo "VITE_PW_BASE_URL=https://dev.deutscher-maklerverbund.de" >> $GITHUB_ENV

      - name: Update env for production
        if: github.ref == 'refs/heads/production'
        run: |
          echo "DEPLOY_ENVIRONMENT=production" >> $GITHUB_ENV
          echo "VITE_PW_BASE_URL=https://mvp.professional.works" >> $GITHUB_ENV
          echo "VITE_APP_INTERCOM_APP_ID=${{ secrets.VITE_APP_INTERCOM_APP_ID_PROD }}" >> $GITHUB_ENV

      - name: Set Composer Secret for Dependabot
        if: github.actor == 'dependabot[bot]'
        run: echo "COMPOSER_SECRET=${{ secrets.COMPOSER_SECRET }}" >> $GITHUB_ENV

      - uses: actions/checkout@v4

      - name: Setup PHP with composer v2
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          tools: composer:v2
          coverage: none
          github-token: ${{ secrets.PRIVATE_TOKEN }}

      - name: Get composer cache directory
        id: composercache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('backend/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install private repo token
        run: composer config --global github-oauth.github.com ${{ env.COMPOSER_SECRET }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-progress --optimize-autoloader

      - name: Setup node
        uses: ./.github/actions/setup-node
        with:
          version: 20

      - name: Start deployment
        uses: bobheadxi/deployments@v1
        id: deployment
        with:
          step: start
          token: ${{ secrets.GITHUB_TOKEN }}
          env: ${{ env.DEPLOY_ENVIRONMENT }}

      - name: Deploy code
        run: ./vendor/bin/vapor deploy ${{ env.APP_ENV }} --commit="${{ github.sha }}" -vv
        env:
          APP_ENV: ${{ env.DEPLOY_ENVIRONMENT }}
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}

      - name: Update deployment status
        uses: bobheadxi/deployments@v1
        if: always()
        with:
          step: finish
          token: ${{ secrets.GITHUB_TOKEN }}
          status: ${{ job.status }}
          env: ${{ steps.deployment.outputs.env }}
          deployment_id: ${{ steps.deployment.outputs.deployment_id }}

  lint-node:
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
      - uses: actions/checkout@v4

      - name: Setup node
        uses: ./.github/actions/setup-node
        with:
          version: 20

      - name: Run linter
        run: yarn run lint

      - name: Run typescript compiler
        run: yarn run tsc

  vitest:
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
      - uses: actions/checkout@v4

      - name: Setup node
        uses: ./.github/actions/setup-node
        with:
          version: 20

      - name: Run vitest
        run: yarn run test

  cypress:
    if: github.ref != 'refs/heads/dev' && github.ref != 'refs/heads/main' && github.ref != 'refs/heads/production'
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        containers: [1, 2, 3]

    services:
      mysql:
        image: mysql
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: yes
          MYSQL_DATABASE: cypress
        ports:
          - 33033:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      minio:
        # fixme: let's not depend on external unofficial image
        image: lazybit/minio
        ports:
          - 9000:9000
        env:
          # fixme: for lazybit/minio it needs deprecated keys
          # one should use MINIO_ROOT_USER, MINIO_ROOT_PASSWORD
          MINIO_ACCESS_KEY: minio
          MINIO_SECRET_KEY: minio123
          # MINIO_ROOT_USER: minio
          # MINIO_ROOT_PASSWORD: minio123
        options: --name=minio --health-cmd "curl http://localhost:9000/minio/health/live"

    steps:
      # We create the s3 bucket for our minio, it's a bit convoluted, there may be a better way
      # we download the mc binary, and use it to create the bucket
      - run: wget --tries=5 --timeout=60 https://dl.min.io/client/mc/release/linux-amd64/mc
      - run: chmod +x ./mc
      - run: bash -c "until (./mc alias set minio http://127.0.0.1:9000 minio minio123) do echo 'waiting for minio to become reachable' && sleep 1; done;"
      - run: ./mc mb --ignore-existing minio/demv-ci-vorgaenge
      # end of minio bucket creation

      - uses: actions/checkout@v4
        # populate commit message for merge commits
        # see ://currents.dev/readme/ci-setup/github-actions
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup PHP with composer v2
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          tools: composer:v2
          extensions: :opcache
          coverage: none
          github-token: ${{ secrets.PRIVATE_TOKEN }}

      - name: Get composer cache directory
        id: composercache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('backend/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Set Composer Secret
        run: echo "COMPOSER_SECRET=${{ secrets.PRIVATE_TOKEN }}" >> $GITHUB_ENV

      - name: Set Composer Secret for Dependabot
        if: github.actor == 'dependabot[bot]'
        run: echo "COMPOSER_SECRET=${{ secrets.COMPOSER_SECRET }}" >> $GITHUB_ENV

      - name: Install private repo token
        run: composer config --global github-oauth.github.com ${{ env.COMPOSER_SECRET }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-progress --optimize-autoloader

      - name: Copy CI ENV
        run: cp .env.ci .env

      - name: Setup node
        uses: ./.github/actions/setup-node
        with:
          version: 20

      - name: Cache cypress binary
        uses: actions/cache@v4
        id: cypress-cache
        with:
          path: /home/<USER>/.cache/Cypress
          key: ${{ runner.os }}-${{ matrix.node-version }}-cypress-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.node-version }}-cypress-

      - name: Build site
        run: yarn run production

      - name: install Cypress if not in cache
        run: CYPRESS_DOWNLOAD_MIRROR=https://cy-cdn.currents.dev yarn cypress install

      - name: Run Cypress on currents.dev
        uses: cypress-io/github-action@v6
        with:
          install: false
          start: php artisan serve -q --env=ci
          command: yarn cypress-cloud run --config baseUrl=http://localhost:8000 --env command_prefix=,command_suffix= --record --parallel --browser chrome --key ${{ secrets.CURRENTS_RECORD_KEY }} --ci-build-id ${{ github.repository }}-${{ github.run_id }}-${{ github.run_attempt}}
