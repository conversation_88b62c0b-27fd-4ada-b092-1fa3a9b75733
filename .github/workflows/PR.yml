name: PR Title Validation
on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize
    branches:
      - main
jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      # Please look up the latest version from
      # https://github.com/amannn/action-semantic-pull-request/releases
      - uses: amannn/action-semantic-pull-request@v3.4.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
