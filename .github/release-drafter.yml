name-template: 'v$RESOLVED_VERSION 🌈'
tag-template: 'v$RESOLVED_VERSION'
template: |
  # What's Changed in $RESOLVED_VERSION
  $CHANGES
categories:
  - title: '🚀 Features'
    labels:
      - 'feature'
      - 'enhancement'
  - title: '🐛 Bug Fixes'
    labels:
      - 'fix'
      - 'bugfix'
      - 'bug'
  - title: '🧰 Maintenance'
    labels:
      - 'chore'
      - 'docs'
      - 'refactor'
change-template: '- $TITLE @$AUTHOR (#$NUMBER)'
change-title-escapes: '\<*_&' # You can add # and @ to disable mentions, and add ` to disable code blocks.
autolabeler:
  - label: 'chore'
    title:
      - '/build.+/'
      - '/ci.+/'
      - '/perf.+/'
      - '/test.+/'
  - label: 'docs'
    title:
      - '/docs.+/'
  - label: 'feature'
    title:
      - '/feat.+/'
  - label: 'fix'
    title:
      - '/fix.+/'
  - label: 'refactor'
    title:
      - '/refactor.+/'
  # Here we define which title results in a major, minor or patch label
  #see: https://regex101.com/r/7NDcjA/1
  - label: 'major'
    title:
      - '/(fix|feat|build|chore|ci|docs|style|refactor|perf|test)(\(\)||\((.*?)\))\!:/'
  - label: 'patch'
    title:
      - '/(fix|build|chore|ci|docs|style|refactor|perf|test)(\(\)||\((.*?)\)):[^(\!)]/'
  - label: 'minor'
    title:
      - '/(feat)(\(\)||\((.*?)\)):[^(\!)]/'

version-resolver:
  major:
    labels:
      - 'major'
  minor:
    labels:
      - 'minor'
  patch:
    labels:
      - 'patch'
  default: patch

