# config file for ./workflows/labeler.yml

tests:
- changed-files:
  - any-glob-to-any-file:
    - resources/js/**/*.spec.ts
    - cypress/**/*
    - tests/**/*

javascript:
- changed-files:
  - any-glob-to-any-file:
    - resources/js/**/*
    - package.json

php:
- changed-files:
  - any-glob-to-any-file:
    - app/**/*
    - tests/**/*
    - composer.json

dependencies:
- changed-files:
  - any-glob-to-any-file:
    - package.json
    - composer.json

github_actions:
- changed-files:
  - any-glob-to-any-file:
    - .github/**/*.yml

migrations:
- changed-files:
  - any-glob-to-any-file:
    - database/migrations/*

deployment:
- base-branch: 'production'
