name: Setup node environment
description: Checks out the repo, sets up cache, installs dependencies enables cancel workflow

inputs:
  version:
    description: The node version to use
    required: true

runs:
  using: 'composite'
  steps:
    - uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.version }}

    - name: Get yarn cache directory
      id: yarn-cache-dir
      shell: bash
      run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT

    - name: Cache yarn cache
      uses: actions/cache@v4
      id: yarn-cache
      with:
        path: ${{ steps.yarn-cache-dir.outputs.dir }}
        key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
        restore-keys: |
          ${{ runner.os }}-yarn-

    - name: Cache node_modules
      uses: actions/cache@v4
      id: node_modules-cache
      with:
        path: node_modules
        key: ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-${{ hashFiles('**/yarn.lock') }}
        restore-keys: |
          ${{ runner.os }}-${{ matrix.node-version }}-nodemodules-

    - name: Install dependencies
      shell: bash
      # if yarn or node_modules cache didn't hit execute install
      if: steps.yarn-cache.outputs.cache-hit != 'true' || steps.node_modules-cache.outputs.cache-hit != 'true'
      run: yarn install --frozen-lockfile --non-interactive
